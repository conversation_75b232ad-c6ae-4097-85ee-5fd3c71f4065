# 相机参数配置文件示例
# 格式: fx fy cx cy image_width image_height
# 
# 这些参数来自相机标定结果
# fx, fy: 焦距 (像素单位)
# cx, cy: 主点坐标 (像素单位)  
# image_width, image_height: 图像尺寸 (像素)
#
# 示例1: 典型的1080p摄像头参数
1000.0 1000.0 960.0 540.0 1920 1080

# 示例2: 如果您使用了getcameraparameter.py标定，
# 请将标定结果按上述格式填入一行
# 例如标定结果为:
# K = [[800.5, 0, 320.2],
#      [0, 801.3, 240.1], 
#      [0, 0, 1]]
# 图像尺寸为640x480，则填入:
# 800.5 801.3 320.2 240.1 640 480
