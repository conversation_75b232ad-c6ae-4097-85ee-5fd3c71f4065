# RT-<PERSON>o<PERSON><PERSON><PERSON> Jetson部署指南

## 📋 概述

这个脚本 `jetson_deployment.py` 是专门为NVIDIA Jetson设备设计的RT-MonoDepth部署工具，解决了以下关键问题：

1. ✅ **权重文件加载** - 正确加载预训练的encoder.pth和depth.pth
2. ✅ **相机参数配置** - 支持自定义相机内参矩阵
3. ✅ **TensorRT优化** - 自动转换和缓存TensorRT模型
4. ✅ **实时推理** - 支持摄像头实时深度估计

## 🚀 快速开始

### 1. 环境准备

```bash
# 确保已安装PyTorch for Jetson和torch2trt
# 参考: https://forums.developer.nvidia.com/t/pytorch-for-jetson-version-1-11-now-available/72048

# 安装依赖
pip3 install opencv-python numpy
```

### 2. 准备权重文件

```bash
# 下载预训练权重到指定目录
mkdir -p weights/RTMonoDepth/s/m_640_192/
# 将 encoder.pth 和 depth.pth 放入该目录
```

### 3. 配置相机参数（可选）

如果使用自己的相机，需要先进行标定：

```bash
# 1. 使用标定工具获取相机参数
python getcameraparameter.py

# 2. 创建相机参数文件
echo "fx fy cx cy image_width image_height" > my_camera_params.txt
# 例如: echo "800.5 801.3 320.2 240.1 640 480" > my_camera_params.txt
```

### 4. 运行部署脚本

#### 性能测试（推荐先运行）

```bash
# 使用小模型进行性能测试
python downsetup/jetson_deployment.py \
    --model_type small \
    --weights_folder weights/RTMonoDepth/s/m_640_192/ \
    --use_tensorrt \
    --benchmark_cycles 200

# 使用自定义相机参数
python downsetup/jetson_deployment.py \
    --model_type small \
    --weights_folder weights/RTMonoDepth/s/m_640_192/ \
    --camera_params my_camera_params.txt \
    --use_tensorrt
```

#### 实时摄像头测试

```bash
# 启动摄像头实时深度估计
python downsetup/jetson_deployment.py \
    --model_type small \
    --weights_folder weights/RTMonoDepth/s/m_640_192/ \
    --use_tensorrt \
    --camera_test \
    --camera_id 0
```

## 📊 参数说明

### 必需参数
- `--weights_folder`: 权重文件夹路径，包含encoder.pth和depth.pth

### 模型参数
- `--model_type`: 模型类型 (`small` 或 `full`)
  - `small`: RTMonoDepth_s，速度更快，推荐用于实时应用
  - `full`: RTMonoDepth，精度更高但速度较慢

### 输入尺寸
- `--input_height`: 输入图像高度 (默认: 192)
- `--input_width`: 输入图像宽度 (默认: 640)

### 相机参数
- `--camera_params`: 相机参数文件路径
  - 文件格式: `fx fy cx cy image_width image_height`
  - 如不指定，使用KITTI默认参数

### TensorRT优化
- `--use_tensorrt`: 启用TensorRT优化（强烈推荐）
- `--force_convert`: 强制重新转换TensorRT模型

### 测试参数
- `--benchmark_cycles`: 性能测试循环次数 (默认: 200)
- `--camera_test`: 启用摄像头实时测试
- `--camera_id`: 摄像头设备ID (默认: 0)

## 🔧 相机参数配置详解

### 方法1: 使用标定工具

```bash
# 1. 准备棋盘格标定图片
mkdir calib_images
# 将标定图片放入 calib_images/ 目录

# 2. 运行标定
python downsetup/getcameraparameter.py

# 3. 记录输出的相机内参矩阵K
# 例如输出:
# 相机内参矩阵 K = 
# [[800.5   0.  320.2]
#  [  0.  801.3 240.1]
#  [  0.    0.    1. ]]

# 4. 创建参数文件
echo "800.5 801.3 320.2 240.1 640 480" > my_camera_params.txt
```

### 方法2: 使用已知参数

如果您已知相机参数，直接创建参数文件：

```bash
# 格式: fx fy cx cy image_width image_height
echo "1000.0 1000.0 960.0 540.0 1920 1080" > camera_params.txt
```

## 📈 性能优化建议

### 1. 模型选择
- **实时应用**: 使用 `--model_type small`
- **高精度需求**: 使用 `--model_type full`

### 2. 输入尺寸优化
```bash
# 高性能配置 (推荐)
--input_height 192 --input_width 640

# 极速配置
--input_height 128 --input_width 416

# 高精度配置
--input_height 256 --input_width 832
```

### 3. TensorRT优化
- 首次运行会进行TensorRT转换，需要几分钟时间
- 转换后的模型会自动缓存，后续启动很快
- 强烈建议使用 `--use_tensorrt` 参数

## 🎯 预期性能

在Jetson Orin NX上的预期性能：

| 配置 | 模型 | 输入尺寸 | TensorRT | 预期FPS |
|------|------|----------|----------|---------|
| 极速 | small | 128×416 | ✅ | 80-100 |
| 推荐 | small | 192×640 | ✅ | 60-80 |
| 高精度 | small | 256×832 | ✅ | 40-60 |
| 最高精度 | full | 192×640 | ✅ | 30-45 |

## 🔍 故障排除

### 1. 权重文件加载失败
```
❌ 编码器权重文件不存在: /path/to/encoder.pth
```
**解决方案**: 检查权重文件路径，确保encoder.pth和depth.pth存在

### 2. TensorRT转换失败
```
RuntimeError: TensorRT conversion failed
```
**解决方案**: 
- 检查SWAP内存是否足够 (建议4GB)
- 尝试不使用TensorRT: 去掉 `--use_tensorrt` 参数

### 3. 摄像头打开失败
```
❌ 无法打开摄像头 0
```
**解决方案**:
- 检查摄像头连接
- 尝试不同的camera_id: `--camera_id 1`
- 检查摄像头权限: `sudo chmod 666 /dev/video*`

### 4. 内存不足
**解决方案**:
- 关闭其他应用程序
- 使用更小的输入尺寸
- 增加SWAP内存

## 📝 使用示例

### 完整的部署流程示例

```bash
# 1. 性能测试
python downsetup/jetson_deployment.py \
    --model_type small \
    --weights_folder weights/RTMonoDepth/s/m_640_192/ \
    --use_tensorrt \
    --benchmark_cycles 100

# 2. 摄像头测试
python downsetup/jetson_deployment.py \
    --model_type small \
    --weights_folder weights/RTMonoDepth/s/m_640_192/ \
    --use_tensorrt \
    --camera_test \
    --camera_params my_camera_params.txt

# 3. 自定义配置测试
python downsetup/jetson_deployment.py \
    --model_type small \
    --weights_folder weights/RTMonoDepth/s/m_640_192/ \
    --input_height 128 \
    --input_width 416 \
    --use_tensorrt \
    --camera_test
```

这样您就可以完整地在Jetson设备上部署RT-MonoDepth了！
