#!/usr/bin/env python3
"""
相机标定工具 - 支持实时标定和批量图片标定
使用方法:
1. 实时标定: python getcameraparameter.py --live --camera_id 0
2. 图片标定: python getcameraparameter.py --images calib_images/*.jpg
"""

import cv2
import numpy as np
import glob
import argparse
import os
import time

def create_calibration_dirs():
    """创建标定所需的目录"""
    os.makedirs('calib_images', exist_ok=True)
    print("📁 已创建 calib_images 目录")

def get_csi_camera_pipeline(camera_id=0, width=1280, height=720, fps=30):
    """构建CSI摄像头的GStreamer管道"""
    return (
        f"nvarguscamerasrc sensor-id={camera_id} ! "
        f"video/x-raw(memory:NVMM), width={width}, height={height}, "
        f"format=NV12, framerate={fps}/1 ! "
        f"nvvidconv flip-method=0 ! "
        f"video/x-raw, width={width}, height={height}, format=BGRx ! "
        f"videoconvert ! "
        f"video/x-raw, format=BGR ! appsink"
    )

def capture_calibration_images(camera_id=0, num_images=20, use_csi=False):
    """实时捕获标定图片"""
    print(f"📷 开始实时捕获标定图片，目标: {num_images} 张")
    print("按 SPACE 键捕获图片，按 'q' 退出")

    if use_csi:
        # 使用CSI摄像头
        print(f"🔌 使用CSI摄像头 {camera_id}")
        gst_pipeline = get_csi_camera_pipeline(camera_id, 1280, 720, 30)
        cap = cv2.VideoCapture(gst_pipeline, cv2.CAP_GSTREAMER)
    else:
        # 使用USB摄像头
        print(f"🔌 使用USB摄像头 {camera_id}")
        cap = cv2.VideoCapture(camera_id)
        if cap.isOpened():
            # 设置摄像头分辨率
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)

    if not cap.isOpened():
        print(f"❌ 无法打开摄像头 {camera_id}")
        if use_csi:
            print("💡 提示：请确保CSI摄像头正确连接")
        return False

    captured_count = 0
    pattern_size = (8, 6)  # 棋盘格内角点数量 (9列7行棋盘格的内角点为8x6)

    while captured_count < num_images:
        ret, frame = cap.read()
        if not ret:
            print("❌ 无法读取摄像头帧")
            break

        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # 检测棋盘格角点
        ret_corners, corners = cv2.findChessboardCorners(gray, pattern_size, None)

        # 显示检测结果
        if ret_corners:
            cv2.drawChessboardCorners(frame, pattern_size, corners, ret_corners)
            cv2.putText(frame, "Chessboard Detected! Press SPACE to capture",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        else:
            cv2.putText(frame, "No chessboard detected",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

        cv2.putText(frame, f"Captured: {captured_count}/{num_images}",
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        cv2.imshow('Camera Calibration', frame)

        key = cv2.waitKey(1) & 0xFF
        if key == ord(' ') and ret_corners:  # 空格键且检测到棋盘格
            filename = f"calib_images/calib_{captured_count:03d}.jpg"
            cv2.imwrite(filename, frame)
            print(f"✅ 已保存: {filename}")
            captured_count += 1
        elif key == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()

    print(f"📸 共捕获 {captured_count} 张标定图片")
    return captured_count > 0

def calibrate_camera_from_images(image_pattern='calib_images/*.jpg'):
    """从图片进行相机标定"""
    # 棋盘格内角点数量 (行, 列) — 这是角点数，不是格子数
    # 9列7行的棋盘格，内角点为8x6
    pattern_size = (8, 6)

    # 实际棋盘格方格尺寸 (单位：mm)
    square_size = 25.0  # 25毫米

    # 准备棋盘格3D点坐标，z=0平面上
    objp = np.zeros((pattern_size[0]*pattern_size[1], 3), np.float32)
    objp[:, :2] = np.mgrid[0:pattern_size[0], 0:pattern_size[1]].T.reshape(-1, 2)
    objp *= square_size

    # 存储所有图片的3D点和2D点
    objpoints = []  # 3D点 (世界坐标)
    imgpoints = []  # 2D点 (图像坐标)

    # 读取所有标定图片
    images = glob.glob(image_pattern)

    if len(images) == 0:
        print(f"❌ 未找到标定图片: {image_pattern}")
        return None

    print(f"📂 找到 {len(images)} 张标定图片")

    valid_images = 0
    for fname in images:
        img = cv2.imread(fname)
        if img is None:
            continue

        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 查找棋盘格角点
        ret, corners = cv2.findChessboardCorners(gray, pattern_size, None)

        if ret:
            objpoints.append(objp)

            # 精确角点检测
            corners2 = cv2.cornerSubPix(gray, corners, (11,11), (-1,-1),
                                        criteria=(cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001))
            imgpoints.append(corners2)
            valid_images += 1

            print(f"✅ 处理成功: {os.path.basename(fname)}")
        else:
            print(f"❌ 未找到角点: {os.path.basename(fname)}")

    if valid_images < 10:
        print(f"❌ 有效图片数量不足 ({valid_images} < 10)，请增加标定图片")
        return None

    print(f"📊 使用 {valid_images} 张有效图片进行标定...")

    # 标定相机
    ret, K, dist_coeffs, rvecs, tvecs = cv2.calibrateCamera(
        objpoints, imgpoints, gray.shape[::-1], None, None)

    if not ret:
        print("❌ 相机标定失败")
        return None

    # 计算重投影误差
    mean_error = 0
    for i in range(len(objpoints)):
        imgpoints2, _ = cv2.projectPoints(objpoints[i], rvecs[i], tvecs[i], K, dist_coeffs)
        error = cv2.norm(imgpoints[i], imgpoints2, cv2.NORM_L2)/len(imgpoints2)
        mean_error += error

    mean_error = mean_error/len(objpoints)

    # 显示结果
    print("\n" + "="*50)
    print("📊 相机标定结果:")
    print("="*50)
    print("相机内参矩阵 K = ")
    print(K)
    print(f"\n图像尺寸: {gray.shape[1]} x {gray.shape[0]}")
    print(f"平均重投影误差: {mean_error:.4f} 像素")

    if mean_error < 1.0:
        print("✅ 标定质量: 优秀")
    elif mean_error < 2.0:
        print("⚠️  标定质量: 良好")
    else:
        print("❌ 标定质量: 较差，建议重新标定")

    # 生成相机参数文件
    fx, fy = K[0, 0], K[1, 1]
    cx, cy = K[0, 2], K[1, 2]
    width, height = gray.shape[1], gray.shape[0]

    param_line = f"{fx:.2f} {fy:.2f} {cx:.2f} {cy:.2f} {width} {height}"

    with open('my_camera_params.txt', 'w') as f:
        f.write(param_line + '\n')

    print(f"\n📄 相机参数已保存到: my_camera_params.txt")
    print(f"参数内容: {param_line}")
    print("\n💡 使用方法:")
    print("python downsetup/jetson_deployment.py \\")
    print("    --model_type small \\")
    print("    --weights_folder weights/RTMonoDepth/s/m_640_192/ \\")
    print("    --camera_params my_camera_params.txt \\")
    print("    --use_tensorrt \\")
    print("    --camera_test")

    return K, dist_coeffs, mean_error

def main():
    parser = argparse.ArgumentParser(description='相机标定工具')
    parser.add_argument('--live', action='store_true', help='实时捕获标定图片')
    parser.add_argument('--camera_id', type=int, default=0, help='摄像头ID')
    parser.add_argument('--num_images', type=int, default=20, help='捕获图片数量')
    parser.add_argument('--images', type=str, default='calib_images/*.jpg',
                       help='标定图片路径模式')
    parser.add_argument('--csi', action='store_true', help='使用CSI摄像头')

    args = parser.parse_args()

    create_calibration_dirs()

    if args.live:
        # 实时捕获模式
        success = capture_calibration_images(args.camera_id, args.num_images, args.csi)
        if not success:
            return

    # 进行标定
    result = calibrate_camera_from_images(args.images)
    if result is None:
        print("❌ 标定失败")
    else:
        print("✅ 标定完成")

if __name__ == "__main__":
    main()
