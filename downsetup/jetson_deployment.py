#!/usr/bin/env python3
"""
RT-Mono<PERSON><PERSON><PERSON>on部署脚本
支持权重加载、相机参数配置和TensorRT优化
"""

import argparse
import os
import time
import cv2
import numpy as np
import torch
import torch.nn.functional as F
from torch2trt import torch2trt, TRTModule

# 导入模型
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from networks.RTMonoDepth.RTMonoDepth import RTMonoDepth
from networks.RTMonoDepth.RTMonoDepth_s import RTMonoDepth as RTMonoDepth_s


class CustomDatasetConfig:
    """自定义相机参数配置类"""
    def __init__(self, camera_params=None):
        if camera_params is None:
            # 默认使用KITTI参数（归一化后的）
            self.K = np.array([[0.58, 0,    0.5, 0],
                              [0,    1.92, 0.5, 0],
                              [0,    0,    1,   0],
                              [0,    0,    0,   1]], dtype=np.float32)
        else:
            self.K = camera_params
    
    def set_custom_camera_params(self, fx, fy, cx, cy, image_width, image_height):
        """
        设置自定义相机参数
        Args:
            fx, fy: 焦距
            cx, cy: 主点坐标
            image_width, image_height: 图像尺寸
        """
        # 归一化相机参数
        fx_norm = fx / image_width
        fy_norm = fy / image_height
        cx_norm = cx / image_width
        cy_norm = cy / image_height
        
        self.K = np.array([[fx_norm, 0, cx_norm, 0],
                          [0, fy_norm, cy_norm, 0],
                          [0, 0, 1, 0],
                          [0, 0, 0, 1]], dtype=np.float32)
        
        print(f"设置自定义相机参数:")
        print(f"原始参数: fx={fx}, fy={fy}, cx={cx}, cy={cy}")
        print(f"归一化参数: fx_norm={fx_norm:.4f}, fy_norm={fy_norm:.4f}")
        print(f"归一化参数: cx_norm={cx_norm:.4f}, cy_norm={cy_norm:.4f}")


class JetsonDepthEstimator:
    """Jetson深度估计器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 相机参数配置
        self.camera_config = CustomDatasetConfig()
        if args.camera_params:
            self.load_camera_params_from_file(args.camera_params)
        
        # 加载模型
        self.model = self.load_model()
        
        # TensorRT优化
        if args.use_tensorrt:
            self.model_trt = self.convert_to_tensorrt()
        else:
            self.model_trt = None
    
    def load_camera_params_from_file(self, camera_file):
        """从文件加载相机参数"""
        if os.path.exists(camera_file):
            # 假设文件格式为: fx fy cx cy width height
            with open(camera_file, 'r') as f:
                line = f.readline().strip()
                params = list(map(float, line.split()))
                if len(params) == 6:
                    fx, fy, cx, cy, width, height = params
                    self.camera_config.set_custom_camera_params(
                        fx, fy, cx, cy, int(width), int(height)
                    )
                else:
                    print(f"相机参数文件格式错误: {camera_file}")
        else:
            print(f"相机参数文件不存在: {camera_file}")
    
    def load_model(self):
        """加载模型和权重"""
        print("正在加载模型...")
        
        # 选择模型类型
        if self.args.model_type == 'small':
            model = RTMonoDepth_s()
            print("使用RTMonoDepth_s (小模型)")
        else:
            model = RTMonoDepth()
            print("使用RTMonoDepth (完整模型)")
        
        model = model.to(self.device)
        
        # 加载预训练权重
        if self.args.weights_folder and os.path.exists(self.args.weights_folder):
            self.load_pretrained_weights(model)
        else:
            print("警告: 未找到权重文件，使用随机初始化!")
        
        model.eval()
        return model
    
    def load_pretrained_weights(self, model):
        """加载预训练权重"""
        weights_folder = self.args.weights_folder
        
        # 加载编码器权重
        encoder_path = os.path.join(weights_folder, "encoder.pth")
        if os.path.exists(encoder_path):
            print(f"加载编码器权重: {encoder_path}")
            encoder_dict = torch.load(encoder_path, map_location=self.device)
            
            # 清理不需要的键
            cleaned_dict = {}
            for k, v in encoder_dict.items():
                if k not in ['height', 'width', 'use_stereo']:
                    cleaned_dict[k] = v
            
            model.encoder.load_state_dict(cleaned_dict, strict=False)
            print("✅ 编码器权重加载成功")
        else:
            print(f"❌ 编码器权重文件不存在: {encoder_path}")
        
        # 加载解码器权重
        depth_path = os.path.join(weights_folder, "depth.pth")
        if os.path.exists(depth_path):
            print(f"加载解码器权重: {depth_path}")
            depth_dict = torch.load(depth_path, map_location=self.device)
            model.decoder.load_state_dict(depth_dict, strict=False)
            print("✅ 解码器权重加载成功")
        else:
            print(f"❌ 解码器权重文件不存在: {depth_path}")
    
    def convert_to_tensorrt(self):
        """转换为TensorRT优化模型"""
        print("开始TensorRT转换...")

        # 创建示例输入
        h, w = self.args.input_height, self.args.input_width
        example_input = torch.randn(1, 3, h, w).to(self.device)

        # 检查是否已存在TensorRT模型文件
        trt_model_path = f"trt_model_{self.args.model_type}_{h}x{w}.pth"

        if os.path.exists(trt_model_path) and not self.args.force_convert:
            print(f"加载已存在的TensorRT模型: {trt_model_path}")
            try:
                model_trt = TRTModule()
                model_trt.load_state_dict(torch.load(trt_model_path))
                print("✅ TensorRT模型加载成功")
                return model_trt
            except Exception as e:
                print(f"⚠️ TensorRT模型加载失败: {e}")
                print("将重新转换模型...")

        try:
            print("转换PyTorch模型到TensorRT...")
            print("⚠️ 这可能需要几分钟时间，请耐心等待...")

            # 设置模型为评估模式
            self.model.eval()

            # 预热模型
            with torch.no_grad():
                _ = self.model(example_input)

            # 转换为TensorRT
            model_trt = torch2trt(
                self.model,
                [example_input],
                fp16_mode=True,
                max_workspace_size=1<<30,  # 1GB
                max_batch_size=1
                # 移除use_onnx=True以避免ONNX依赖问题
            )

            # 验证TensorRT模型
            print("验证TensorRT模型...")
            with torch.no_grad():
                pytorch_output = self.model(example_input)
                trt_output = model_trt(example_input)

                if isinstance(pytorch_output, dict):
                    pytorch_result = pytorch_output[("disp", 0)]
                else:
                    pytorch_result = pytorch_output

                # 计算差异
                diff = torch.abs(pytorch_result - trt_output).mean()
                print(f"PyTorch vs TensorRT 平均差异: {diff:.6f}")

                if diff < 0.01:
                    print("✅ TensorRT模型验证通过")
                else:
                    print("⚠️ TensorRT模型精度可能有损失")

            # 保存TensorRT模型
            torch.save(model_trt.state_dict(), trt_model_path)
            print(f"TensorRT模型已保存: {trt_model_path}")

        except Exception as e:
            print(f"❌ TensorRT转换失败: {e}")
            print("将使用原始PyTorch模型")
            return None

        print("✅ TensorRT转换完成")
        return model_trt
    
    def preprocess_image(self, image):
        """图像预处理"""
        # 调整大小
        h, w = self.args.input_height, self.args.input_width
        image_resized = cv2.resize(image, (w, h))
        
        # BGR转RGB
        image_rgb = cv2.cvtColor(image_resized, cv2.COLOR_BGR2RGB)
        
        # 归一化到[0,1]
        image_norm = image_rgb.astype(np.float32) / 255.0
        
        # 转换为tensor
        image_tensor = torch.from_numpy(image_norm).permute(2, 0, 1).unsqueeze(0)
        
        return image_tensor.to(self.device)
    
    def predict_depth(self, image):
        """深度预测"""
        # 预处理
        input_tensor = self.preprocess_image(image)
        
        # 推理
        with torch.no_grad():
            start_time = time.time()
            
            if self.model_trt is not None:
                # 使用TensorRT模型
                depth_output = self.model_trt(input_tensor)
            else:
                # 使用原始PyTorch模型
                outputs = self.model(input_tensor)
                if isinstance(outputs, dict):
                    depth_output = outputs[("disp", 0)]
                else:
                    depth_output = outputs
            
            inference_time = time.time() - start_time
        
        # 后处理
        depth_map = depth_output.cpu().numpy().squeeze()
        
        return depth_map, inference_time
    
    def run_benchmark(self):
        """运行性能测试"""
        print("开始性能测试...")
        
        h, w = self.args.input_height, self.args.input_width
        dummy_image = np.random.randint(0, 255, (h, w, 3), dtype=np.uint8)
        
        # 预热
        print("预热中...")
        for _ in range(10):
            _, _ = self.predict_depth(dummy_image)
        
        # 正式测试
        times = []
        for i in range(self.args.benchmark_cycles):
            _, inference_time = self.predict_depth(dummy_image)
            times.append(inference_time)
            if (i + 1) % 50 == 0:
                print(f"已完成 {i + 1}/{self.args.benchmark_cycles} 次测试")
        
        # 统计结果
        avg_time = np.mean(times)
        fps = 1.0 / avg_time
        
        print(f"\n📊 性能测试结果:")
        print(f"平均推理时间: {avg_time*1000:.2f} ms")
        print(f"平均FPS: {fps:.1f}")
        print(f"模型类型: {self.args.model_type}")
        print(f"输入尺寸: {h}x{w}")
        print(f"TensorRT优化: {'是' if self.args.use_tensorrt else '否'}")


def parse_args():
    parser = argparse.ArgumentParser(description='RT-MonoDepth Jetson部署工具')
    
    # 模型相关参数
    parser.add_argument('--model_type', type=str, default='small', 
                       choices=['small', 'full'], help='模型类型')
    parser.add_argument('--weights_folder', type=str, required=True,
                       help='权重文件夹路径')
    
    # 输入尺寸
    parser.add_argument('--input_height', type=int, default=192, help='输入图像高度')
    parser.add_argument('--input_width', type=int, default=640, help='输入图像宽度')
    
    # 相机参数
    parser.add_argument('--camera_params', type=str, 
                       help='相机参数文件路径 (格式: fx fy cx cy width height)')
    
    # TensorRT相关
    parser.add_argument('--use_tensorrt', action='store_true', 
                       help='使用TensorRT优化')
    parser.add_argument('--force_convert', action='store_true',
                       help='强制重新转换TensorRT模型')
    
    # 测试相关
    parser.add_argument('--benchmark_cycles', type=int, default=200,
                       help='性能测试循环次数')
    parser.add_argument('--camera_test', action='store_true',
                       help='运行摄像头实时测试')
    parser.add_argument('--camera_id', type=int, default=0,
                       help='摄像头ID')
    parser.add_argument('--use_csi', action='store_true',
                       help='使用CSI摄像头')
    
    return parser.parse_args()


def run_camera_test(estimator, args):
    """运行摄像头实时测试"""
    print(f"正在打开摄像头 {args.camera_id}...")

    if args.use_csi:
        # 使用CSI摄像头
        print(f"🔌 使用CSI摄像头 {args.camera_id}")
        gst_pipeline = get_csi_camera_pipeline(args.camera_id, 1280, 720, 30)
        cap = cv2.VideoCapture(gst_pipeline, cv2.CAP_GSTREAMER)
    else:
        # 使用USB摄像头
        print(f"🔌 使用USB摄像头 {args.camera_id}")
        cap = cv2.VideoCapture(args.camera_id)
        if cap.isOpened():
            # 设置摄像头分辨率
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
            cap.set(cv2.CAP_PROP_FPS, 30)

    if not cap.isOpened():
        print(f"❌ 无法打开摄像头 {args.camera_id}")
        if args.use_csi:
            print("💡 提示：请确保CSI摄像头正确连接")
        return

    print("✅ 摄像头已打开")
    print("按 'q' 退出, 按 's' 保存当前帧")

    frame_count = 0
    fps_history = []

    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                print("❌ 无法读取摄像头帧")
                break

            # 深度估计
            depth_map, inference_time = estimator.predict_depth(frame)

            # 计算FPS
            fps = 1.0 / inference_time
            fps_history.append(fps)
            if len(fps_history) > 30:  # 保持最近30帧的FPS记录
                fps_history.pop(0)
            avg_fps = np.mean(fps_history)

            # 深度图可视化
            depth_normalized = (depth_map - depth_map.min()) / (depth_map.max() - depth_map.min())
            depth_colored = cv2.applyColorMap(
                (depth_normalized * 255).astype(np.uint8),
                cv2.COLORMAP_JET
            )

            # 调整深度图大小以匹配原图
            original_h, original_w = frame.shape[:2]
            depth_colored = cv2.resize(depth_colored, (original_w, original_h))

            # 添加信息文本
            info_text = [
                f"FPS: {avg_fps:.1f}",
                f"推理时间: {inference_time*1000:.1f}ms",
                f"模型: {args.model_type}",
                f"TensorRT: {'ON' if args.use_tensorrt else 'OFF'}",
                f"帧数: {frame_count}"
            ]

            # 在原图上添加文本
            y_offset = 30
            for text in info_text:
                cv2.putText(frame, text, (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                y_offset += 25

            # 在深度图上添加标题
            cv2.putText(depth_colored, "Depth Map", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

            # 显示结果
            cv2.imshow('Original + Info', frame)
            cv2.imshow('Depth Map', depth_colored)

            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # 保存当前帧
                timestamp = int(time.time())
                cv2.imwrite(f'frame_{timestamp}.jpg', frame)
                cv2.imwrite(f'depth_{timestamp}.jpg', depth_colored)
                print(f"已保存帧: frame_{timestamp}.jpg, depth_{timestamp}.jpg")

            frame_count += 1

    except KeyboardInterrupt:
        print("\n用户中断测试")

    finally:
        cap.release()
        cv2.destroyAllWindows()
        print(f"摄像头测试结束，共处理 {frame_count} 帧")


if __name__ == '__main__':
    args = parse_args()

    print("🚀 RT-MonoDepth Jetson部署工具")
    print("=" * 50)

    # 创建深度估计器
    estimator = JetsonDepthEstimator(args)

    if args.camera_test:
        print("开始摄像头实时测试...")
        run_camera_test(estimator, args)
    else:
        # 运行性能测试
        estimator.run_benchmark()


def get_csi_camera_pipeline(camera_id=0, width=1280, height=720, fps=30):
    """构建CSI摄像头的GStreamer管道"""
    return (
        f"nvarguscamerasrc sensor-id={camera_id} ! "
        f"video/x-raw(memory:NVMM), width={width}, height={height}, "
        f"format=NV12, framerate={fps}/1 ! "
        f"nvvidconv flip-method=0 ! "
        f"video/x-raw, width={width}, height={height}, format=BGRx ! "
        f"videoconvert ! "
        f"video/x-raw, format=BGR ! appsink"
    )

def run_camera_test(estimator, args):
    """运行摄像头实时测试"""
    print(f"正在打开摄像头 {args.camera_id}...")

    if args.use_csi:
        # 使用CSI摄像头
        print(f"🔌 使用CSI摄像头 {args.camera_id}")
        gst_pipeline = get_csi_camera_pipeline(args.camera_id, 1280, 720, 30)
        cap = cv2.VideoCapture(gst_pipeline, cv2.CAP_GSTREAMER)
    else:
        # 使用USB摄像头
        print(f"🔌 使用USB摄像头 {args.camera_id}")
        cap = cv2.VideoCapture(args.camera_id)
        if cap.isOpened():
            # 设置摄像头分辨率
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
            cap.set(cv2.CAP_PROP_FPS, 30)

    if not cap.isOpened():
        print(f"❌ 无法打开摄像头 {args.camera_id}")
        if args.use_csi:
            print("💡 提示：请确保CSI摄像头正确连接")
        return

    print("✅ 摄像头已打开")
    print("按 'q' 退出, 按 's' 保存当前帧")

    frame_count = 0
    fps_history = []

    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                print("❌ 无法读取摄像头帧")
                break

            # 深度估计
            depth_map, inference_time = estimator.predict_depth(frame)

            # 计算FPS
            fps = 1.0 / inference_time
            fps_history.append(fps)
            if len(fps_history) > 30:  # 保持最近30帧的FPS记录
                fps_history.pop(0)
            avg_fps = np.mean(fps_history)

            # 深度图可视化
            depth_normalized = (depth_map - depth_map.min()) / (depth_map.max() - depth_map.min())
            depth_colored = cv2.applyColorMap(
                (depth_normalized * 255).astype(np.uint8),
                cv2.COLORMAP_JET
            )

            # 调整深度图大小以匹配原图
            original_h, original_w = frame.shape[:2]
            depth_colored = cv2.resize(depth_colored, (original_w, original_h))

            # 添加信息文本
            info_text = [
                f"FPS: {avg_fps:.1f}",
                f"推理时间: {inference_time*1000:.1f}ms",
                f"模型: {args.model_type}",
                f"TensorRT: {'ON' if args.use_tensorrt else 'OFF'}",
                f"帧数: {frame_count}"
            ]

            # 在原图上添加文本
            y_offset = 30
            for text in info_text:
                cv2.putText(frame, text, (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                y_offset += 25

            # 在深度图上添加标题
            cv2.putText(depth_colored, "Depth Map", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

            # 显示结果
            cv2.imshow('Original + Info', frame)
            cv2.imshow('Depth Map', depth_colored)

            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # 保存当前帧
                timestamp = int(time.time())
                cv2.imwrite(f'frame_{timestamp}.jpg', frame)
                cv2.imwrite(f'depth_{timestamp}.jpg', depth_colored)
                print(f"已保存帧: frame_{timestamp}.jpg, depth_{timestamp}.jpg")

            frame_count += 1

    except KeyboardInterrupt:
        print("\n用户中断测试")

    finally:
        cap.release()
        cv2.destroyAllWindows()
        print(f"摄像头测试结束，共处理 {frame_count} 帧")
