#!/bin/bash
# 使用GStreamer拍摄标定图片

echo "📷 使用GStreamer拍摄标定图片"
echo "=============================="

# 创建保存目录
mkdir -p calib_images

echo "操作说明："
echo "1. 准备好9列7行棋盘格标定板"
echo "2. 脚本会每3秒自动拍摄一张图片"
echo "3. 请在每次拍摄间隔内调整棋盘格位置和角度"
echo "4. 按Ctrl+C停止拍摄"
echo ""

read -p "按Enter开始拍摄..." dummy

# 拍摄20张图片
for i in {1..20}; do
    echo "正在拍摄第 $i 张图片..."
    
    # 使用GStreamer拍摄
    gst-launch-1.0 -e nvarguscamerasrc sensor-id=0 num-buffers=1 ! \
        'video/x-raw(memory:NVMM), width=1280, height=720, format=NV12, framerate=30/1' ! \
        nvvidconv ! \
        'video/x-raw, format=BGRx' ! \
        videoconvert ! \
        'video/x-raw, format=BGR' ! \
        jpegenc ! \
        filesink location=calib_images/calib_$(printf "%03d" $i).jpg
    
    if [ $? -eq 0 ]; then
        echo "✅ 已保存: calib_images/calib_$(printf "%03d" $i).jpg"
    else
        echo "❌ 拍摄失败"
        break
    fi
    
    if [ $i -lt 20 ]; then
        echo "请调整棋盘格位置，3秒后拍摄下一张..."
        sleep 3
    fi
done

echo ""
echo "📸 拍摄完成！"
echo "下一步：运行标定程序"
echo "python3 downsetup/getcameraparameter.py --images 'calib_images/*.jpg'"
