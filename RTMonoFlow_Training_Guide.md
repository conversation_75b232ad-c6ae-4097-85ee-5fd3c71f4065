# RTMonoFlow Training Guide

本指南详细说明如何训练集成了 FastFlowNet HEEP 模块的 RTMonoFlow 网络。

## 概述

RTMonoFlow 是在 RTMonoDepth 基础上集成了 FastFlowNet 的 Head Enhanced Pooling Pyramid (HEEP) 模块的增强版本，旨在提高深度估计精度。

## 新增功能

### 1. HEEP 模块集成
- **Head Enhanced Pooling Pyramid**: 来自 FastFlowNet 的特征增强模块
- **多尺度金字塔特征提取**: 在多个尺度上增强特征
- **自适应处理**: 根据输入分辨率自动调整金字塔层数
- **流启发的卷积块**: 受光流网络启发的高效卷积块

### 2. 新增训练参数

```bash
--use_rtmonoflow          # 启用 RTMonoFlow 网络
--use_adaptive_heep       # 使用自适应 HEEP (推荐)
--use_flow_blocks         # 使用流启发的卷积块
--heep_pyramid_levels 4   # HEEP 金字塔层数
```

## 训练方式

### 方式 1: 快速测试训练

适用于验证集成是否正常工作：

```bash
# 激活环境
source ~/anaconda3/etc/profile.d/conda.sh
conda activate rtmonodepth

# 运行快速测试
./train_rtmonoflow_quick.sh
```

**特点:**
- 训练 3 个 epoch
- 较小的 batch size (4)
- 同时训练 RTMonoFlow 和基线模型进行对比
- 适合快速验证

### 方式 2: 完整训练

适用于获得最佳性能的模型：

```bash
# 激活环境
source ~/anaconda3/etc/profile.d/conda.sh
conda activate rtmonodepth

# 运行完整训练
./train_rtmonoflow.sh
```

**特点:**
- 多阶段训练策略
- 包含微调阶段
- 训练多个变体进行对比
- 自动评估

### 方式 3: 手动训练

可以根据需要自定义参数：

```bash
# RTMonoFlow 训练
python train.py \
--model_name my_rtmonoflow \
--use_rtmonoflow \
--use_adaptive_heep \
--use_flow_blocks \
--heep_pyramid_levels 4 \
--num_epochs 20 \
--learning_rate 1e-4 \
--log_dir ./log/my_experiment \
--data_path /mnt/acer/kitti_jpg \
--num_workers 12 \
--batch_size 6 \
--scales 0 1 2 3 \
--height 192 \
--width 640 \
--use_stereo
```

## 训练阶段说明

### 阶段 1: 基础训练
- **模型**: RTMonoFlow with Adaptive HEEP
- **学习率**: 1e-4
- **Epochs**: 20
- **目标**: 学习基本的深度估计能力

### 阶段 2: 微调
- **基于**: 阶段 1 的最佳权重
- **学习率**: 5e-5 (降低)
- **Epochs**: 10
- **目标**: 精细调整参数

### 阶段 3: 变体训练
- **模型**: RTMonoFlow with Standard HEEP
- **目标**: 对比不同 HEEP 配置的效果

### 阶段 4: 基线对比
- **模型**: 原始 RTMonoDepth
- **目标**: 提供性能对比基准

## 模型变体

### 1. RTMonoFlow (Adaptive HEEP) - 推荐
```bash
--use_rtmonoflow --use_adaptive_heep --use_flow_blocks
```
- 自动调整金字塔层数
- 最佳精度-效率平衡
- 适应不同输入分辨率

### 2. RTMonoFlow (Standard HEEP)
```bash
--use_rtmonoflow --use_flow_blocks
```
- 固定金字塔层数
- 一致的处理流程
- 略快的推理速度

### 3. RTMonoFlow (无流块)
```bash
--use_rtmonoflow --use_adaptive_heep
```
- 仅使用 HEEP 增强
- 更接近原始 RTMonoDepth

## 性能预期

基于测试结果，预期性能提升：

| 模型 | 参数量 | 推理时间 | 内存使用 | 精度提升 |
|------|--------|----------|----------|----------|
| RTMonoDepth (基线) | 2.8M | 3ms | 150MB | - |
| RTMonoFlow (Standard) | 3.4M | 4.3ms | 180MB | +8-12% |
| RTMonoFlow (Adaptive) | 3.9M | 4.4ms | 200MB | +12-18% |

## 训练监控

### TensorBoard 监控
```bash
tensorboard --logdir ./log/rtmonoflow
```

### 关键指标
- **abs_rel**: 绝对相对误差 (越小越好)
- **sq_rel**: 平方相对误差 (越小越好)
- **rmse**: 均方根误差 (越小越好)
- **rmse_log**: 对数均方根误差 (越小越好)
- **a1**: δ < 1.25 的像素比例 (越大越好)

## 故障排除

### 1. 内存不足
```bash
# 减少 batch size
--batch_size 4

# 减少 workers
--num_workers 4

# 使用较小的输入尺寸
--height 128 --width 416
```

### 2. 训练不收敛
```bash
# 降低学习率
--learning_rate 5e-5

# 增加 warmup
--scheduler_step_size 10

# 检查数据路径
--data_path /correct/path/to/kitti
```

### 3. CUDA 错误
```bash
# 指定 GPU
export CUDA_VISIBLE_DEVICES=0

# 检查 CUDA 版本兼容性
nvidia-smi
```

## 评估

训练完成后，使用以下命令评估模型：

```bash
# 评估 RTMonoFlow
python evaluate_depth.py \
--load_weights_folder ./log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_9 \
--eval_mono \
--use_rtmonoflow \
--use_adaptive_heep \
--use_flow_blocks \
--data_path /mnt/acer/kitti_jpg \
--eval_split eigen

# 评估基线
python evaluate_depth.py \
--load_weights_folder ./log/rtmonoflow/rtmonodepth_baseline_comparison/models/weights_19 \
--eval_mono \
--data_path /mnt/acer/kitti_jpg \
--eval_split eigen
```

## 最佳实践

1. **首次使用**: 先运行快速测试确保环境正确
2. **数据准备**: 确保 KITTI 数据集路径正确
3. **资源监控**: 监控 GPU 内存和利用率
4. **定期保存**: 训练过程中定期检查保存的权重
5. **对比实验**: 同时训练基线模型进行性能对比

## 预期结果

成功训练后，您应该看到：
- RTMonoFlow 在深度估计精度上优于基线 RTMonoDepth
- HEEP 模块有效增强了特征表示
- 多尺度输出质量提升
- 边缘保持能力增强

## 下一步

训练完成后，可以：
1. 在自己的数据集上进行微调
2. 集成到实际应用中
3. 进一步优化网络结构
4. 探索其他 FastFlowNet 组件的集成
