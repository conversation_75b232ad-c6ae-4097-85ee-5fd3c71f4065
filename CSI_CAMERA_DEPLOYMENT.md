# 🎥 CSI摄像头部署指南

## 📋 针对CSI摄像头的完整部署流程

既然你使用的是CSI摄像头，所有操作都需要在Orin NX开发板上完成。

## 🚀 **快速开始（推荐）**

```bash
# 1. 直接运行快速部署脚本
chmod +x quick_deploy.sh
./quick_deploy.sh

# 2. 按照提示选择：
#    - 摄像头类型：选择 "1) CSI摄像头"
#    - 标定方式：选择 "1) 实时标定"
#    - 然后按照界面提示完成标定和测试
```

## 🔧 **手动步骤（如果需要更多控制）**

### 第一步：CSI摄像头标定

```bash
# 使用CSI摄像头进行实时标定
python3 downsetup/getcameraparameter.py --live --camera_id 0 --num_images 20 --csi

# 操作说明：
# 1. 准备9x6棋盘格标定板（方格大小25mm）
# 2. 将标定板放在CSI摄像头前
# 3. 当屏幕显示"Chessboard Detected!"时按空格键捕获
# 4. 从不同角度和距离捕获20张图片
# 5. 按'q'退出，程序自动进行标定
```

**标定技巧：**
- 📐 确保标定板平整，无弯曲
- 🔄 从不同角度捕获：正面、左倾、右倾、上倾、下倾
- 📏 从不同距离捕获：近距离、中距离、远距离
- 💡 确保光线充足，避免反光

### 第二步：性能测试

```bash
# 使用自定义相机参数进行性能测试
python3 downsetup/jetson_deployment.py \
    --model_type small \
    --weights_folder weights/RTMonoDepth/s/m_640_192/ \
    --camera_params my_camera_params.txt \
    --use_tensorrt \
    --benchmark_cycles 100

# 首次运行会进行TensorRT转换，需要3-5分钟
```

### 第三步：实时深度估计

```bash
# 启动CSI摄像头实时深度估计
python3 downsetup/jetson_deployment.py \
    --model_type small \
    --weights_folder weights/RTMonoDepth/s/m_640_192/ \
    --camera_params my_camera_params.txt \
    --use_tensorrt \
    --camera_test \
    --camera_id 0 \
    --use_csi

# 操作说明：
# - 按 'q' 退出
# - 按 's' 保存当前帧和深度图
# - 实时显示FPS和推理时间
```

## 📊 **CSI摄像头性能优化**

### 不同配置的性能表现：

```bash
# 极速配置 (预期80-100 FPS)
python3 downsetup/jetson_deployment.py \
    --model_type small \
    --weights_folder weights/RTMonoDepth/s/m_640_192/ \
    --camera_params my_camera_params.txt \
    --input_height 128 \
    --input_width 416 \
    --use_tensorrt \
    --camera_test \
    --use_csi

# 推荐配置 (预期60-80 FPS)
python3 downsetup/jetson_deployment.py \
    --model_type small \
    --weights_folder weights/RTMonoDepth/s/m_640_192/ \
    --camera_params my_camera_params.txt \
    --input_height 192 \
    --input_width 640 \
    --use_tensorrt \
    --camera_test \
    --use_csi

# 高精度配置 (预期40-60 FPS)
python3 downsetup/jetson_deployment.py \
    --model_type small \
    --weights_folder weights/RTMonoDepth/s/m_640_192/ \
    --camera_params my_camera_params.txt \
    --input_height 256 \
    --input_width 832 \
    --use_tensorrt \
    --camera_test \
    --use_csi
```

## 🔍 **CSI摄像头故障排除**

### 问题1：CSI摄像头无法打开
```bash
# 检查CSI摄像头连接
dmesg | grep -i camera

# 检查摄像头设备
ls /dev/video*

# 测试CSI摄像头
gst-launch-1.0 nvarguscamerasrc sensor-id=0 ! nvoverlaysink
```

### 问题2：图像质量问题
```bash
# 调整CSI摄像头参数
# 在部署脚本中修改GStreamer管道参数
# 例如调整曝光、增益等
```

### 问题3：性能不佳
```bash
# 确保性能模式设置正确
sudo nvpmodel -m 0
sudo jetson_clocks

# 检查温度
cat /sys/devices/virtual/thermal/thermal_zone*/temp

# 如果温度过高，添加散热
```

## 💡 **CSI摄像头特殊说明**

1. **硬件连接**：
   - 确保CSI摄像头排线连接牢固
   - 注意排线方向，金属触点朝下

2. **软件支持**：
   - CSI摄像头使用GStreamer管道
   - 需要nvargus-daemon服务运行

3. **分辨率支持**：
   - 常见分辨率：1280x720, 1920x1080, 3280x2464
   - 可以根据需要调整

4. **延迟优化**：
   - CSI摄像头通常比USB摄像头延迟更低
   - 适合实时应用

## 🎯 **完整的CSI摄像头部署命令**

```bash
# 一键完成所有步骤
./quick_deploy.sh

# 或者手动执行：

# 1. 标定
python3 downsetup/getcameraparameter.py --live --camera_id 0 --num_images 20 --csi

# 2. 测试
python3 downsetup/jetson_deployment.py \
    --model_type small \
    --weights_folder weights/RTMonoDepth/s/m_640_192/ \
    --camera_params my_camera_params.txt \
    --use_tensorrt \
    --benchmark_cycles 100

# 3. 实时运行
python3 downsetup/jetson_deployment.py \
    --model_type small \
    --weights_folder weights/RTMonoDepth/s/m_640_192/ \
    --camera_params my_camera_params.txt \
    --use_tensorrt \
    --camera_test \
    --use_csi
```

这样你就可以完全在Orin NX开发板上完成CSI摄像头的标定和部署了！
