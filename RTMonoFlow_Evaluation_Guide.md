# RTMonoFlow 评估指南

本指南介绍如何使用新创建的 `evaluate_flow.py` 脚本来评估 RTMonoFlow 模型。

## 概述

`evaluate_flow.py` 是专门为 RTMonoFlow 模型设计的评估脚本，基于 `evaluate_depth_full.py` 改进而来，具有以下特点：

- **支持 RTMonoFlow 模型**：包括 Adaptive HEEP 和 Standard HEEP 变体
- **基线模型比较**：可以同时评估 RTMonoFlow 和原始 RTMonoDepth 进行对比
- **性能分析**：提供推理时间、FPS 和精度指标
- **兼容性好**：使用与其他评估脚本相同的 MonodepthOptions 框架

## 环境准备

### 1. 激活 Conda 环境

```bash
conda activate rtmonodepth
```

### 2. 验证环境

运行简单测试来验证环境是否正确设置：

```bash
python simple_test.py
```

或者运行完整的环境测试：

```bash
./test_rtmonoflow_env.sh
```

## 使用方法

### 1. 基本用法

```bash
python evaluate_flow.py \
    --load_weights_folder /path/to/rtmonoflow/weights \
    --data_path /path/to/kitti/dataset \
    --eval_mono
```

### 2. 完整参数示例

```bash
python evaluate_flow.py \
    --load_weights_folder ./log/rtmonoflow/rtmonoflow_adaptive_heep/models/weights_19 \
    --model_type adaptive \
    --baseline_weights ./log/rtmonoflow/rtmonodepth_baseline/models/weights_19 \
    --data_path /mnt/acer/kitti_jpg \
    --eval_mono \
    --eval_split eigen \
    --save_pred_disps \
    --output_dir ./evaluation_results \
    --num_workers 8 \
    --batch_size 16
```

### 3. 使用便捷脚本

我们提供了一个便捷的 shell 脚本来运行完整的评估：

```bash
# 使用默认路径
./evaluate_rtmonoflow.sh

# 使用自定义路径
./evaluate_rtmonoflow.sh /path/to/model /path/to/data /path/to/baseline
```

## 参数说明

### 必需参数

- `--load_weights_folder`: RTMonoFlow 模型权重文件夹路径
- `--data_path`: KITTI 数据集路径
- `--eval_mono` 或 `--eval_stereo`: 选择单目或双目评估模式

### RTMonoFlow 特定参数

- `--model_type`: RTMonoFlow 模型类型
  - `adaptive`: Adaptive HEEP（推荐）
  - `standard`: Standard HEEP
  - `no_flow`: 不使用 flow-inspired blocks

- `--baseline_weights`: 基线 RTMonoDepth 模型权重路径（用于对比）

### 其他重要参数

- `--eval_split`: 评估数据集分割（`eigen`, `eigen_benchmark`, `benchmark`）
- `--save_pred_disps`: 保存预测的视差图
- `--output_dir`: 结果输出目录
- `--post_process`: 启用后处理
- `--num_workers`: 数据加载器工作进程数
- `--batch_size`: 批处理大小

## 模型权重文件

评估脚本支持多种权重文件命名约定：

1. **RTMonoFlow 单文件**：
   - `rtmonoflow.pth`
   - `model.pth`
   - `rtmonoflow_model.pth`

2. **分离的编码器/解码器文件**：
   - `encoder.pth` + `depth.pth`

## 输出结果

### 1. 控制台输出

脚本会在控制台显示详细的评估结果，包括：

```
RTMONOFLOW EVALUATION RESULTS
================================================================================
Model                     abs_rel  sq_rel   rmse     rmse_log a1       a2       a3       FPS     
--------------------------------------------------------------------------------
RTMonoFlow (adaptive)     0.115    0.903    4.863    0.193    0.873    0.958    0.980    45.2
RTMonoDepth (Baseline)    0.121    0.956    5.142    0.201    0.864    0.954    0.979    52.1

IMPROVEMENT ANALYSIS
============================================================
RTMonoFlow (adaptive):
  abs_rel improvement: +5.0%
  rmse improvement: +5.4%
  a1 improvement: +1.0%
  FPS change: -13.2%
```

### 2. 保存的文件

如果使用 `--save_pred_disps` 参数，会保存：

- 预测的视差图：`disps_ModelName_split.npy`
- 保存在指定的输出目录中

## 评估指标说明

- **abs_rel**: 平均相对误差（越小越好）
- **sq_rel**: 平方相对误差（越小越好）
- **rmse**: 均方根误差（越小越好）
- **rmse_log**: 对数均方根误差（越小越好）
- **a1, a2, a3**: 阈值精度（越大越好）
- **FPS**: 每秒帧数（推理速度）

## 故障排除

### 1. 导入错误

如果遇到模块导入错误：

```bash
# 检查环境
conda list

# 安装缺失的包
pip install torch torchvision opencv-python scikit-image
```

### 2. RTMonoFlow 导入失败

确保 RTMonoFlow 模块正确安装：

```bash
# 检查文件是否存在
ls networks/RTMonoDepth/RTMonoFlow.py
ls networks/RTMonoDepth/heep_module.py
```

### 3. 权重文件未找到

检查权重文件路径和命名：

```bash
# 列出可用的权重文件
find ./log -name "*.pth" -type f
```

### 4. CUDA 内存不足

减少批处理大小：

```bash
python evaluate_flow.py --batch_size 8 [其他参数]
```

## 示例工作流程

### 1. 训练后评估

```bash
# 1. 训练 RTMonoFlow
./train_rtmonoflow.sh

# 2. 评估训练好的模型
./evaluate_rtmonoflow.sh

# 3. 查看结果
ls evaluation_results/
```

### 2. 自定义评估

```bash
# 评估特定模型
python evaluate_flow.py \
    --load_weights_folder ./my_model/weights \
    --model_type adaptive \
    --data_path /data/kitti \
    --eval_mono \
    --save_pred_disps
```

## 性能优化建议

1. **批处理大小**：根据 GPU 内存调整 `--batch_size`
2. **工作进程**：设置合适的 `--num_workers`（通常为 CPU 核心数）
3. **数据预加载**：确保数据存储在快速存储设备上
4. **混合精度**：RTMonoFlow 支持 FP16 推理以提高速度

## 扩展功能

评估脚本设计为可扩展的，您可以：

1. 添加新的评估指标
2. 支持其他数据集
3. 集成可视化功能
4. 添加模型分析工具

有关更多详细信息，请参考源代码中的注释和文档字符串。
