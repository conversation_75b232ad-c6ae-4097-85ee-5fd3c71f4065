#!/usr/bin/env python3
"""
检测CSI摄像头支持的分辨率
"""

import cv2
import subprocess
import re
import time

def detect_csi_camera_modes():
    """检测CSI摄像头支持的模式"""
    print("🔍 检测CSI摄像头支持的分辨率...")
    
    # 使用gst-inspect检测摄像头能力
    try:
        # 运行一个简短的gstreamer命令来获取摄像头信息
        cmd = [
            "gst-launch-1.0", "-v", "nvarguscamerasrc", "sensor-id=0", "num-buffers=1", "!",
            "video/x-raw(memory:NVMM)", "!", "fakesink"
        ]
        
        print("正在检测摄像头模式...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        # 解析输出中的分辨率信息
        output = result.stderr + result.stdout
        
        # 查找GST_ARGUS输出中的分辨率信息
        resolution_pattern = r'(\d+) x (\d+) FR = ([\d.]+) fps'
        matches = re.findall(resolution_pattern, output)
        
        if matches:
            print("\n📊 检测到的摄像头模式:")
            print("分辨率\t\t帧率")
            print("-" * 30)
            for width, height, fps in matches:
                print(f"{width}x{height}\t{fps} fps")
            
            return matches
        else:
            print("⚠️ 无法从输出中解析分辨率信息")
            print("原始输出:")
            print(output)
            return []
            
    except subprocess.TimeoutExpired:
        print("⚠️ 检测超时")
        return []
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        return []

def test_csi_resolution(width, height, fps=30):
    """测试特定分辨率是否可用"""
    print(f"🧪 测试分辨率 {width}x{height}@{fps}fps...")
    
    gst_pipeline = (
        f"nvarguscamerasrc sensor-id=0 ! "
        f"video/x-raw(memory:NVMM), width={width}, height={height}, "
        f"format=NV12, framerate={fps}/1 ! "
        f"nvvidconv flip-method=0 ! "
        f"video/x-raw, width={width}, height={height}, format=BGRx ! "
        f"videoconvert ! "
        f"video/x-raw, format=BGR ! appsink"
    )
    
    cap = cv2.VideoCapture(gst_pipeline, cv2.CAP_GSTREAMER)
    
    if cap.isOpened():
        # 尝试读取几帧
        success_count = 0
        for i in range(5):
            ret, frame = cap.read()
            if ret and frame is not None:
                success_count += 1
                if i == 0:  # 检查第一帧的实际尺寸
                    actual_height, actual_width = frame.shape[:2]
                    print(f"  实际获得: {actual_width}x{actual_height}")
            time.sleep(0.1)
        
        cap.release()
        
        if success_count >= 3:
            print(f"  ✅ 成功 - 读取了 {success_count}/5 帧")
            return True
        else:
            print(f"  ❌ 失败 - 只读取了 {success_count}/5 帧")
            return False
    else:
        print(f"  ❌ 无法打开摄像头")
        return False

def test_common_resolutions():
    """测试常见分辨率"""
    print("\n🧪 测试常见分辨率...")
    
    common_resolutions = [
        (640, 480, 30),
        (1280, 720, 30),
        (1920, 1080, 30),
        (3280, 2464, 21),  # 根据之前的输出
        (3280, 1848, 28),
        (1640, 1232, 30),
    ]
    
    working_resolutions = []
    
    for width, height, fps in common_resolutions:
        if test_csi_resolution(width, height, fps):
            working_resolutions.append((width, height, fps))
    
    return working_resolutions

def get_current_camera_params():
    """获取当前相机参数文件的信息"""
    try:
        with open('my_camera_params.txt', 'r') as f:
            line = f.readline().strip()
            params = list(map(float, line.split()))
            if len(params) == 6:
                fx, fy, cx, cy, width, height = params
                print(f"\n📄 当前相机参数文件:")
                print(f"  焦距: fx={fx:.2f}, fy={fy:.2f}")
                print(f"  主点: cx={cx:.2f}, cy={cy:.2f}")
                print(f"  分辨率: {int(width)}x{int(height)}")
                return int(width), int(height)
    except FileNotFoundError:
        print("\n📄 未找到相机参数文件 (my_camera_params.txt)")
    except Exception as e:
        print(f"\n📄 读取相机参数文件失败: {e}")
    
    return None, None

def recommend_resolution(detected_modes, working_resolutions):
    """推荐最佳分辨率"""
    print("\n💡 分辨率推荐:")
    
    # 获取当前参数文件的分辨率
    param_width, param_height = get_current_camera_params()
    
    if working_resolutions:
        print("\n✅ 可用分辨率:")
        for width, height, fps in working_resolutions:
            usage = ""
            if width == 1920 and height == 1080:
                usage = " (推荐用于标定 - 高精度)"
            elif width == 1280 and height == 720:
                usage = " (推荐用于实时处理 - 平衡性能)"
            elif width == 640 and height == 480:
                usage = " (推荐用于高速处理)"
            
            current_marker = ""
            if param_width == width and param_height == height:
                current_marker = " ← 当前参数文件使用此分辨率"
            
            print(f"  {width}x{height}@{fps}fps{usage}{current_marker}")
    
    print("\n📋 建议:")
    print("1. 🎯 标定时使用: 1920x1080 (获得最高精度)")
    print("2. 🚀 实时处理使用: 1920x1080 (与标定保持一致)")
    print("3. ⚡ 如需更高性能: 1280x720 (需重新标定)")

def main():
    print("🔍 CSI摄像头分辨率检测工具")
    print("=" * 40)
    
    # 检测摄像头模式
    detected_modes = detect_csi_camera_modes()
    
    # 测试常见分辨率
    working_resolutions = test_common_resolutions()
    
    # 推荐分辨率
    recommend_resolution(detected_modes, working_resolutions)
    
    print("\n🔧 下一步操作:")
    if working_resolutions:
        # 找到最高可用分辨率
        best_res = max(working_resolutions, key=lambda x: x[0] * x[1])
        width, height, fps = best_res
        
        print(f"1. 使用推荐分辨率 {width}x{height} 重新标定:")
        print(f"   python3 downsetup/getcameraparameter.py --live --camera_id 0 --num_images 20 --csi")
        print(f"2. 使用相同分辨率进行深度估计:")
        print(f"   python3 downsetup/jetson_deployment.py --model_type small --weights_folder weights/RTMonoDepth/s/m_640_192/ --camera_params my_camera_params.txt --camera_test --use_csi")
    else:
        print("❌ 未检测到可用分辨率，请检查摄像头连接")

if __name__ == "__main__":
    main()
