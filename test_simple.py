#!/usr/bin/env python3
"""
最简单的环境测试
"""

import os
import sys

print("=== RTMonoFlow 环境测试 ===")

# 1. 检查conda环境
conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'None')
print(f"当前conda环境: {conda_env}")

if conda_env == 'rtmonodepth':
    print("✓ 正确的conda环境")
else:
    print("✗ 请运行: conda activate rtmonodepth")
    sys.exit(1)

# 2. 检查Python版本
print(f"Python版本: {sys.version}")

# 3. 检查基本依赖
try:
    import torch
    print(f"✓ PyTorch {torch.__version__}")
    print(f"✓ CUDA可用: {torch.cuda.is_available()}")
except ImportError:
    print("✗ PyTorch未安装")
    sys.exit(1)

try:
    import cv2
    print(f"✓ OpenCV {cv2.__version__}")
except ImportError:
    print("✗ OpenCV未安装")
    sys.exit(1)

try:
    import numpy as np
    print(f"✓ NumPy {np.__version__}")
except ImportError:
    print("✗ NumPy未安装")
    sys.exit(1)

# 4. 检查项目文件
files = [
    'evaluate_flow.py',
    'options.py', 
    'datasets/__init__.py',
    'networks/RTMonoDepth/RTMonoFlow.py'
]

print("\n=== 项目文件检查 ===")
for f in files:
    if os.path.exists(f):
        print(f"✓ {f}")
    else:
        print(f"✗ {f}")

# 5. 检查模型权重
print("\n=== 模型权重检查 ===")
model_path = './log/rtmonoflow_test/rtmonoflow_quick_test/models/weights_2'
baseline_path = './log/rtmonoflow_test/rtmonodepth_baseline_quick/models/weights_2'

if os.path.exists(model_path):
    print(f"✓ RTMonoFlow模型: {model_path}")
else:
    print(f"✗ RTMonoFlow模型未找到: {model_path}")

if os.path.exists(baseline_path):
    print(f"✓ 基线模型: {baseline_path}")
else:
    print(f"✗ 基线模型未找到: {baseline_path}")

# 6. 检查数据路径
data_path = '/mnt/acer/kitti_jpg'
if os.path.exists(data_path):
    print(f"✓ 数据路径: {data_path}")
else:
    print(f"✗ 数据路径未找到: {data_path}")

print("\n=== 测试完成 ===")
print("如果所有项目都显示✓，您可以运行:")
print("python evaluate_flow.py --load_weights_folder ./log/rtmonoflow_test/rtmonoflow_quick_test/models/weights_2 --model_type adaptive --eval_mono --data_path /mnt/acer/kitti_jpg --eval_split eigen --baseline_weights ./log/rtmonoflow_test/rtmonodepth_baseline_quick/models/weights_2 --num_workers 4 --batch_size 8")
