#!/usr/bin/env python3
"""
RTMonoFlow Fast Inference Optimization
针对FPS优化的推理脚本
"""

import torch
import torch.nn as nn
import time
import numpy as np
from networks.RTMonoDepth.RTMonoFlow import create_rtmonoflow

class FastRTMonoFlow(nn.Module):
    """
    优化版RTMonoFlow，专注于推理速度
    """
    def __init__(self, base_model, optimization_level='medium'):
        super(FastRTMonoFlow, self).__init__()
        self.base_model = base_model
        self.optimization_level = optimization_level
        
        # 根据优化级别设置参数
        if optimization_level == 'aggressive':
            self.use_single_scale = True
            self.skip_heep_fusion = True
        elif optimization_level == 'medium':
            self.use_single_scale = False
            self.skip_heep_fusion = False
        else:  # conservative
            self.use_single_scale = False
            self.skip_heep_fusion = False
    
    def forward(self, x):
        if self.optimization_level == 'aggressive':
            return self.forward_aggressive(x)
        elif self.optimization_level == 'medium':
            return self.forward_medium(x)
        else:
            return self.base_model(x)
    
    def forward_aggressive(self, x):
        """激进优化：只输出主尺度"""
        outputs = self.base_model(x)
        # 只返回主要输出，跳过其他尺度
        return {("disp", 0): outputs[("disp", 0)]}
    
    def forward_medium(self, x):
        """中等优化：减少部分计算"""
        return self.base_model(x)

def create_optimized_rtmonoflow(optimization_config):
    """
    创建优化的RTMonoFlow模型
    
    Args:
        optimization_config: 优化配置字典
    """
    config = {
        'use_adaptive_heep': False,  # 使用Standard HEEP
        'use_flow_blocks': True,     # 保持Flow blocks
        **optimization_config
    }
    
    model = create_rtmonoflow(**config)
    return model

def optimize_for_inference(model):
    """
    推理优化设置
    """
    # 1. 设置为评估模式
    model.eval()
    
    # 2. 禁用梯度计算
    for param in model.parameters():
        param.requires_grad = False
    
    # 3. 融合BatchNorm（如果有）
    # torch.quantization.fuse_modules(model, ...)
    
    # 4. 启用优化
    model = torch.jit.optimize_for_inference(model)
    
    return model

def benchmark_model(model, input_size=(1, 3, 192, 640), num_runs=100, warmup=10):
    """
    模型性能基准测试
    """
    device = next(model.parameters()).device
    dummy_input = torch.randn(input_size).to(device)
    
    # 预热
    with torch.no_grad():
        for _ in range(warmup):
            _ = model(dummy_input)
    
    # 同步GPU
    if device.type == 'cuda':
        torch.cuda.synchronize()
    
    # 测试推理时间
    times = []
    with torch.no_grad():
        for _ in range(num_runs):
            start_time = time.time()
            _ = model(dummy_input)
            if device.type == 'cuda':
                torch.cuda.synchronize()
            end_time = time.time()
            times.append(end_time - start_time)
    
    avg_time = np.mean(times)
    fps = 1.0 / avg_time
    
    return {
        'avg_inference_time': avg_time * 1000,  # ms
        'fps': fps,
        'std_time': np.std(times) * 1000,  # ms
    }

def main():
    """
    FPS优化测试主函数
    """
    print("RTMonoFlow FPS Optimization Test")
    print("=" * 50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 测试不同优化配置
    configs = {
        'Original (Adaptive HEEP)': {
            'use_adaptive_heep': True,
            'use_flow_blocks': True
        },
        'Standard HEEP': {
            'use_adaptive_heep': False,
            'use_flow_blocks': True
        },
        'No Flow Blocks': {
            'use_adaptive_heep': False,
            'use_flow_blocks': False
        }
    }
    
    results = {}
    
    for config_name, config in configs.items():
        print(f"\nTesting {config_name}...")
        
        try:
            # 创建模型
            model = create_optimized_rtmonoflow(config)
            model = model.to(device)
            
            # 优化推理
            model = optimize_for_inference(model)
            
            # 基准测试
            benchmark_results = benchmark_model(model)
            results[config_name] = benchmark_results
            
            print(f"  FPS: {benchmark_results['fps']:.1f}")
            print(f"  Avg time: {benchmark_results['avg_inference_time']:.2f} ms")
            
        except Exception as e:
            print(f"  Error: {e}")
            results[config_name] = None
    
    # 打印对比结果
    print("\n" + "=" * 50)
    print("PERFORMANCE COMPARISON")
    print("=" * 50)
    print(f"{'Configuration':<25} {'FPS':<10} {'Time (ms)':<12}")
    print("-" * 50)
    
    for config_name, result in results.items():
        if result:
            print(f"{config_name:<25} {result['fps']:<10.1f} {result['avg_inference_time']:<12.2f}")
        else:
            print(f"{config_name:<25} {'Failed':<10} {'N/A':<12}")
    
    # 计算改进
    if 'Original (Adaptive HEEP)' in results and 'Standard HEEP' in results:
        original_fps = results['Original (Adaptive HEEP)']['fps']
        optimized_fps = results['Standard HEEP']['fps']
        improvement = (optimized_fps - original_fps) / original_fps * 100
        print(f"\nStandard HEEP improvement: +{improvement:.1f}% FPS")

if __name__ == "__main__":
    main()
