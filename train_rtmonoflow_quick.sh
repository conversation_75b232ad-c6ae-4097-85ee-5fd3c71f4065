#!/bin/bash

# Quick RTMonoFlow Training Script
# For testing and validation of the HEEP integration

echo "Quick RTMonoFlow Training Test"
echo "=============================="

# Set environment variables
export CUDA_VISIBLE_DEVICES=0

# Quick training with RTMonoFlow (Adaptive HEEP)
echo "Training RTMonoFlow with Adaptive HEEP (Quick Test)"
python train.py \
--model_name rtmonoflow_quick_test \
--use_rtmonoflow \
--use_adaptive_heep \
--use_flow_blocks \
--heep_pyramid_levels 4 \
--num_epochs 3 \
--learning_rate 1e-4 \
--log_dir ./log/rtmonoflow_test \
--data_path /mnt/acer/kitti_jpg \
--num_workers 8 \
--batch_size 8 \
--scales 0 1 2 \
--height 192 \
--width 640 \
--disparity_smoothness 1e-3 \
--frame_ids 0 -1 1 \
--use_stereo

echo "Quick training completed!"
echo "Check ./log/rtmonoflow_test/rtmonoflow_quick_test for results"

# Optional: Quick comparison with baseline
echo ""
echo "Training baseline RTMonoDepth for comparison (Quick Test)"
python train.py \
--model_name rtmonodepth_baseline_quick \
--num_epochs 3 \
--learning_rate 1e-4 \
--log_dir ./log/rtmonoflow_test \
--data_path /mnt/acer/kitti_jpg \
--num_workers 8 \
--batch_size 4 \
--scales 0 1 2 \
--height 192 \
--width 640 \
--disparity_smoothness 1e-3 \
--frame_ids 0 -1 1 \
--use_stereo

echo "Baseline training completed!"
echo "=============================="
echo "Quick test results:"
echo "- RTMonoFlow: ./log/rtmonoflow_test/rtmonoflow_quick_test"
echo "- Baseline: ./log/rtmonoflow_test/rtmonodepth_baseline_quick"
