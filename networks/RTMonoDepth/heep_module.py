# Copyright 2025. All rights reserved.
#
# HEEP (Head Enhanced Pooling Pyramid) Module
# Adapted from FastFlowNet for RTMonoDepth integration
# Reference: FastFlowNet: A Lightweight Network for Fast Optical Flow Estimation (ICRA 2021)

import torch
import torch.nn as nn
import torch.nn.functional as F


class HeadEnhancedPoolingPyramid(nn.Module):
    """
    Head Enhanced Pooling Pyramid (HEEP) module from FastFlowNet
    Enhances high-resolution pyramid features while reducing parameters
    """
    def __init__(self, in_channels, pyramid_levels=4, reduction_ratio=4):
        super(HeadEnhancedPoolingPyramid, self).__init__()
        
        self.pyramid_levels = pyramid_levels
        self.in_channels = in_channels
        self.reduction_ratio = reduction_ratio
        
        # Head enhancement for high-resolution features
        self.head_enhance = nn.ModuleList()
        for i in range(pyramid_levels):
            # Reduce channels for efficiency
            reduced_channels = max(in_channels // (reduction_ratio * (i + 1)), 16)
            self.head_enhance.append(
                nn.Sequential(
                    nn.Conv2d(in_channels, reduced_channels, 1, bias=False),
                    nn.BatchNorm2d(reduced_channels),
                    nn.ReLU(inplace=True),
                    nn.Conv2d(reduced_channels, reduced_channels, 3, 1, 1, bias=False),
                    nn.BatchNorm2d(reduced_channels),
                    nn.ReLU(inplace=True)
                )
            )
        
        # Pooling pyramid operations
        self.pool_sizes = [1, 2, 4, 8][:pyramid_levels]
        
        # Feature fusion after pooling
        total_channels = sum([max(in_channels // (reduction_ratio * (i + 1)), 16) 
                             for i in range(pyramid_levels)])
        
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(total_channels, in_channels, 1, bias=False),
            nn.BatchNorm2d(in_channels),
            nn.ReLU(inplace=True)
        )
        
        # Residual connection
        self.residual_conv = nn.Sequential(
            nn.Conv2d(in_channels, in_channels, 1, bias=False),
            nn.BatchNorm2d(in_channels)
        )
        
        self.final_activation = nn.ReLU(inplace=True)
        
    def forward(self, x):
        """
        Args:
            x: Input feature map [B, C, H, W]
        Returns:
            Enhanced feature map [B, C, H, W]
        """
        batch_size, channels, height, width = x.shape
        
        # Store original for residual connection
        residual = self.residual_conv(x)
        
        # Apply pooling pyramid with head enhancement
        pyramid_features = []
        
        for i, (pool_size, head_enhance) in enumerate(zip(self.pool_sizes, self.head_enhance)):
            if pool_size == 1:
                # No pooling for the first level
                pooled = x
            else:
                # Adaptive average pooling
                pooled = F.adaptive_avg_pool2d(x, (height // pool_size, width // pool_size))
            
            # Apply head enhancement
            enhanced = head_enhance(pooled)
            
            # Upsample back to original size if needed
            if enhanced.shape[2:] != (height, width):
                enhanced = F.interpolate(enhanced, size=(height, width), 
                                       mode='bilinear', align_corners=False)
            
            pyramid_features.append(enhanced)
        
        # Concatenate all pyramid features
        fused_features = torch.cat(pyramid_features, dim=1)
        
        # Apply fusion convolution
        fused = self.fusion_conv(fused_features)
        
        # Add residual connection
        output = self.final_activation(fused + residual)
        
        return output


class MultiScaleHEEP(nn.Module):
    """
    Multi-scale HEEP module for different encoder levels
    """
    def __init__(self, encoder_channels, pyramid_levels=4):
        super(MultiScaleHEEP, self).__init__()
        
        self.encoder_channels = encoder_channels
        self.heep_modules = nn.ModuleList()
        
        # Create HEEP module for each encoder level
        for channels in encoder_channels:
            # Adjust pyramid levels based on channel count
            levels = min(pyramid_levels, max(2, channels // 32))
            self.heep_modules.append(
                HeadEnhancedPoolingPyramid(channels, levels)
            )
    
    def forward(self, encoder_features):
        """
        Args:
            encoder_features: List of feature maps from encoder
        Returns:
            List of HEEP-enhanced feature maps
        """
        enhanced_features = []
        
        for feat, heep_module in zip(encoder_features, self.heep_modules):
            enhanced_feat = heep_module(feat)
            enhanced_features.append(enhanced_feat)
        
        return enhanced_features


class HEEPFeatureFusion(nn.Module):
    """
    Feature fusion module that combines original and HEEP-enhanced features
    """
    def __init__(self, channels, fusion_weight=0.7):
        super(HEEPFeatureFusion, self).__init__()
        
        self.fusion_weight = fusion_weight
        self.channels = channels
        
        # Learnable fusion weights
        self.fusion_weights = nn.Parameter(torch.ones(2) * 0.5)
        
        # Feature refinement after fusion
        self.refine_conv = nn.Sequential(
            nn.Conv2d(channels, channels, 3, 1, 1, bias=False),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels, channels, 1, bias=False),
            nn.BatchNorm2d(channels)
        )
        
        self.activation = nn.ReLU(inplace=True)
    
    def forward(self, original_feat, heep_feat):
        """
        Args:
            original_feat: Original feature map
            heep_feat: HEEP-enhanced feature map
        Returns:
            Fused feature map
        """
        # Normalize fusion weights
        weights = F.softmax(self.fusion_weights, dim=0)
        
        # Weighted fusion
        fused = weights[0] * original_feat + weights[1] * heep_feat
        
        # Feature refinement
        refined = self.refine_conv(fused)
        
        # Residual connection with original features
        output = self.activation(refined + original_feat)
        
        return output


class AdaptiveHEEP(nn.Module):
    """
    Adaptive HEEP that adjusts pyramid levels based on input resolution
    """
    def __init__(self, in_channels, max_pyramid_levels=6):
        super(AdaptiveHEEP, self).__init__()
        
        self.in_channels = in_channels
        self.max_pyramid_levels = max_pyramid_levels
        
        # Create HEEP modules for different pyramid levels
        self.heep_modules = nn.ModuleDict()
        for levels in range(2, max_pyramid_levels + 1):
            self.heep_modules[f'levels_{levels}'] = HeadEnhancedPoolingPyramid(
                in_channels, levels
            )
    
    def forward(self, x):
        """
        Args:
            x: Input feature map [B, C, H, W]
        Returns:
            HEEP-enhanced feature map
        """
        height, width = x.shape[2:]
        
        # Determine optimal pyramid levels based on resolution
        if height >= 256 and width >= 256:
            levels = min(self.max_pyramid_levels, 6)
        elif height >= 128 and width >= 128:
            levels = min(self.max_pyramid_levels, 4)
        else:
            levels = min(self.max_pyramid_levels, 3)
        
        # Use appropriate HEEP module
        heep_module = self.heep_modules[f'levels_{levels}']
        
        return heep_module(x)


# Utility function for creating HEEP-enhanced encoder
def create_heep_enhanced_encoder(original_encoder, use_multi_scale=True):
    """
    Create a HEEP-enhanced version of an existing encoder
    
    Args:
        original_encoder: Original encoder module
        use_multi_scale: Whether to use multi-scale HEEP
    
    Returns:
        HEEP-enhanced encoder wrapper
    """
    class HEEPEnhancedEncoder(nn.Module):
        def __init__(self, encoder, heep_module):
            super(HEEPEnhancedEncoder, self).__init__()
            self.encoder = encoder
            self.heep_module = heep_module
            self.num_ch_enc = encoder.num_ch_enc
            
        def forward(self, x):
            # Get original encoder features
            original_features = self.encoder(x)
            
            # Apply HEEP enhancement
            enhanced_features = self.heep_module(original_features)
            
            return enhanced_features
    
    # Create appropriate HEEP module
    if use_multi_scale:
        heep_module = MultiScaleHEEP(original_encoder.num_ch_enc)
    else:
        # Use single HEEP for the last feature map
        heep_module = HeadEnhancedPoolingPyramid(original_encoder.num_ch_enc[-1])
    
    return HEEPEnhancedEncoder(original_encoder, heep_module)
