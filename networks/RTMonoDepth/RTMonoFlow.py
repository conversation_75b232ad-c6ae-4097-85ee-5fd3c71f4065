# Copyright 2025. All rights reserved.
#
# RTMonoFlow: R<PERSON><PERSON>Depth enhanced with HEEP from FastFlowNet
# Integrates Head Enhanced Pooling Pyramid for improved depth estimation accuracy
# Reference: FastFlowNet: A Lightweight Network for Fast Optical Flow Estimation (ICRA 2021)

from __future__ import absolute_import, division, print_function

import torch
import torch.nn as nn
import torch.nn.functional as F
from collections import OrderedDict

from .heep_module import (
    HeadEnhancedPoolingPyramid, 
    MultiScaleHEEP, 
    HEEPFeatureFusion,
    AdaptiveHEEP
)


class HEEPEnhancedDepthEncoder(nn.Module):
    """
    RTMonoDepth encoder enhanced with HEEP modules
    """
    def __init__(self, use_adaptive_heep=True):
        super(HEEPEnhancedDepthEncoder, self).__init__()
        
        # Original RTMonoDepth encoder structure
        self.num_ch_enc = [64, 64, 128, 256]
        self.num_ch_enc_build = [64, 64, 128, 256]
        
        # Initial convolution
        self.conv1 = nn.Conv2d(3, 64, kernel_size=3, stride=2, padding=1, bias=False)
        self.relu = nn.ReLU(inplace=True)
        
        # Encoder layers
        self.convs = nn.ModuleList()
        for l, (ch_in, ch_out) in enumerate(zip(self.num_ch_enc_build[:-1], self.num_ch_enc_build[1:])):
            layer = nn.Sequential(
                nn.Conv2d(ch_in, ch_out, kernel_size=3, stride=2, padding=1, bias=True),
                nn.LeakyReLU(0.1, inplace=True),
                nn.Conv2d(ch_out, ch_out, kernel_size=3, stride=1, padding=1, bias=True),
                nn.LeakyReLU(0.1, inplace=True),
                nn.Conv2d(ch_out, ch_out, kernel_size=3, stride=1, padding=1, bias=True),
                nn.LeakyReLU(0.1, inplace=True)
            )
            self.convs.append(layer)
        
        # HEEP enhancement modules
        if use_adaptive_heep:
            self.heep_modules = nn.ModuleList([
                AdaptiveHEEP(ch, max_pyramid_levels=4) for ch in self.num_ch_enc
            ])
        else:
            self.heep_modules = nn.ModuleList([
                HeadEnhancedPoolingPyramid(ch, pyramid_levels=min(4, max(2, ch // 32)))
                for ch in self.num_ch_enc
            ])
        
        # Feature fusion modules
        self.fusion_modules = nn.ModuleList([
            HEEPFeatureFusion(ch) for ch in self.num_ch_enc
        ])
        
        # Initialize weights
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        features = []
        
        # Normalize input
        x = (x - 0.45) / 0.225
        
        # Initial convolution
        x = self.conv1(x)
        x = self.relu(x)
        
        # Apply HEEP enhancement and fusion for first feature
        heep_enhanced = self.heep_modules[0](x)
        fused_feature = self.fusion_modules[0](x, heep_enhanced)
        features.append(fused_feature)
        
        # Process through encoder layers with HEEP enhancement
        for i, conv in enumerate(self.convs):
            x = conv(x)
            
            # Apply HEEP enhancement
            heep_enhanced = self.heep_modules[i + 1](x)
            
            # Fuse original and HEEP-enhanced features
            fused_feature = self.fusion_modules[i + 1](x, heep_enhanced)
            features.append(fused_feature)
        
        return features


class FlowInspiredConvBlock(nn.Module):
    """
    Convolution block inspired by FastFlowNet's efficient design
    """
    def __init__(self, in_channels, out_channels, use_separable=True):
        super(FlowInspiredConvBlock, self).__init__()
        
        if use_separable:
            # Separable convolution for efficiency
            self.conv = nn.Sequential(
                nn.Conv2d(in_channels, in_channels, 3, padding=1, groups=in_channels, bias=False),
                nn.Conv2d(in_channels, out_channels, 1, bias=False),
                nn.BatchNorm2d(out_channels),
                nn.ELU(inplace=True)
            )
        else:
            # Standard convolution
            self.pad = nn.ReflectionPad2d(1)
            self.conv = nn.Conv2d(int(in_channels), int(out_channels), 3)
            self.nonlin = nn.ELU(inplace=True)
    
    def forward(self, x):
        if hasattr(self, 'pad'):
            out = self.pad(x)
            out = self.conv(out)
            out = self.nonlin(out)
        else:
            out = self.conv(x)
        return out


class EnhancedDepthDecoder(nn.Module):
    """
    Enhanced depth decoder with flow-inspired improvements
    """
    def __init__(self, num_ch_enc, scales=range(4), use_skips=True, use_flow_blocks=True):
        super(EnhancedDepthDecoder, self).__init__()

        self.use_skips = use_skips
        self.scales = scales
        self.use_flow_blocks = use_flow_blocks

        self.num_ch_enc = num_ch_enc
        self.num_ch_dec = [16, 32, 64, 128, 256]

        # Decoder convolutions
        self.convs = OrderedDict()
        for i in range(3, -1, -1):
            # Upconv_0
            num_ch_in = self.num_ch_enc[-1] if i == 4 else self.num_ch_dec[i + 1]
            num_ch_out = self.num_ch_dec[i]
            
            if use_flow_blocks:
                self.convs[("upconv", i, 0)] = FlowInspiredConvBlock(num_ch_in, num_ch_out)
            else:
                self.convs[("upconv", i, 0)] = ConvBlock(num_ch_in, num_ch_out)

            # Upconv_1
            num_ch_in = self.num_ch_dec[i]
            if self.use_skips and i == 1:
                num_ch_in += self.num_ch_enc[i - 1]
            num_ch_out = self.num_ch_dec[i]

            if use_flow_blocks:
                self.convs[("upconv", i, 1)] = FlowInspiredConvBlock(num_ch_in, num_ch_out)
            else:
                self.convs[("upconv", i, 1)] = ConvBlock(num_ch_in, num_ch_out)

        # Depth prediction heads
        for s in self.scales:
            self.convs[("dispconv", s)] = EnhancedDepthHead(self.num_ch_dec[s])

        self.decoder = nn.ModuleList(list(self.convs.values()))
        self.sigmoid = nn.Sigmoid()

    def forward(self, input_features):
        self.outputs = {}

        x = input_features[-1]  # Start from deepest feature
        
        for i in range(3, -1, -1):
            x = self.convs[("upconv", i, 0)](x)
            x = nn.functional.interpolate(x, scale_factor=2, mode="nearest")

            if self.use_skips and i > 1:
                x += input_features[i - 1]
            elif self.use_skips and i == 1:
                x = [x, input_features[i - 1]]
                x = torch.cat(x, 1)

            x = self.convs[("upconv", i, 1)](x)

            if i in self.scales:
                depth = self.sigmoid(self.convs[("dispconv", i)](x))
                self.outputs[("disp", i)] = depth

        return self.outputs


class EnhancedDepthHead(nn.Module):
    """
    Enhanced depth prediction head with multi-scale processing
    """
    def __init__(self, in_channels):
        super(EnhancedDepthHead, self).__init__()
        
        self.conv1 = nn.Conv2d(in_channels, 64, 3, 1, 1)
        self.conv2 = nn.Conv2d(64, 32, 3, 1, 1)
        self.conv3 = nn.Conv2d(32, 16, 3, 1, 1)
        self.conv4 = nn.Conv2d(16, 1, 3, 1, 1)
        
        self.relu = nn.LeakyReLU(0.1, inplace=True)
        
        # Multi-scale enhancement
        self.ms_conv = nn.Conv2d(in_channels, 16, 1)
        
    def forward(self, x):
        # Main path
        out = self.relu(self.conv1(x))
        out = self.relu(self.conv2(out))
        out = self.relu(self.conv3(out))
        
        # Multi-scale path
        ms_out = self.ms_conv(x)
        
        # Combine paths
        combined = out + ms_out
        final_out = self.conv4(combined)
        
        return final_out


class ConvBlock(nn.Module):
    """Standard convolution block for compatibility"""
    def __init__(self, in_channels, out_channels):
        super(ConvBlock, self).__init__()

        self.pad = nn.ReflectionPad2d(1)
        self.conv = nn.Conv2d(int(in_channels), int(out_channels), 3)
        self.nonlin = nn.ELU(inplace=True)

    def forward(self, x):
        out = self.pad(x)
        out = self.conv(out)
        out = self.nonlin(out)
        return out


class RTMonoFlow(nn.Module):
    """
    RTMonoFlow: RTMonoDepth enhanced with HEEP from FastFlowNet
    Provides improved depth estimation accuracy through enhanced feature extraction
    """
    def __init__(self, use_adaptive_heep=True, use_flow_blocks=True):
        super(RTMonoFlow, self).__init__()
        
        # HEEP-enhanced encoder
        self.encoder = HEEPEnhancedDepthEncoder(use_adaptive_heep=use_adaptive_heep)
        
        # Enhanced decoder with flow-inspired improvements
        self.decoder = EnhancedDepthDecoder(
            self.encoder.num_ch_enc, 
            use_flow_blocks=use_flow_blocks
        )
        
        # Note: Device placement will be handled by the training script

    def forward(self, x):
        """
        Forward pass through RTMonoFlow
        
        Args:
            x: Input image tensor [B, 3, H, W]
            
        Returns:
            Dictionary containing depth predictions at multiple scales
        """
        # Extract HEEP-enhanced features
        features = self.encoder(x)
        
        # Decode to depth maps
        outputs = self.decoder(features)
        
        return outputs
    
    def get_depth(self, x):
        """
        Convenience method to get the main depth prediction
        
        Args:
            x: Input image tensor [B, 3, H, W]
            
        Returns:
            Depth prediction tensor [B, 1, H, W]
        """
        outputs = self.forward(x)
        return outputs[("disp", 0)]


# Factory function for easy model creation
def create_rtmonoflow(use_adaptive_heep=True, use_flow_blocks=True, pretrained=False):
    """
    Create RTMonoFlow model
    
    Args:
        use_adaptive_heep: Whether to use adaptive HEEP modules
        use_flow_blocks: Whether to use flow-inspired convolution blocks
        pretrained: Whether to load pretrained weights (not implemented yet)
        
    Returns:
        RTMonoFlow model instance
    """
    model = RTMonoFlow(
        use_adaptive_heep=use_adaptive_heep,
        use_flow_blocks=use_flow_blocks
    )
    
    if pretrained:
        # TODO: Implement pretrained weight loading
        print("Pretrained weights not available yet")
    
    return model
