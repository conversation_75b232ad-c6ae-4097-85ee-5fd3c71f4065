# RTMonoFlow 快速评估指南

## 当前状态

根据您的输出，我们发现了以下可用的模型：

### 可用的RTMonoFlow模型：
- `./log/rtmonoflow_test/rtmonoflow_quick_test/models/weights_2`

### 可用的基线模型：
- `./log/rtmonoflow_test/rtmonodepth_baseline_quick/models/weights_2`

## 快速运行步骤

### 1. 确保在正确的conda环境中

```bash
# 激活环境
conda activate rtmonodepth

# 验证环境
echo $CONDA_DEFAULT_ENV
# 应该显示: rtmonodepth
```

### 2. 检查依赖

```bash
# 在rtmonodepth环境中运行
python -c "import torch; print('PyTorch:', torch.__version__)"
python -c "import cv2; print('OpenCV:', cv2.__version__)"
python -c "import numpy; print('NumPy:', numpy.__version__)"

# 如果缺少scikit-image
pip install scikit-image
```

### 3. 运行评估

有三种方式运行评估：

#### 方式1: 使用Python脚本（推荐）

```bash
# 确保在rtmonodepth环境中
conda activate rtmonodepth

# 运行评估
python run_rtmonoflow_eval.py
```

#### 方式2: 直接使用evaluate_flow.py

```bash
# 确保在rtmonodepth环境中
conda activate rtmonodepth

# 运行评估
python evaluate_flow.py \
    --load_weights_folder ./log/rtmonoflow_test/rtmonoflow_quick_test/models/weights_2 \
    --model_type adaptive \
    --eval_mono \
    --data_path /mnt/acer/kitti_jpg \
    --eval_split eigen \
    --baseline_weights ./log/rtmonoflow_test/rtmonodepth_baseline_quick/models/weights_2 \
    --num_workers 4 \
    --batch_size 8 \
    --save_pred_disps \
    --output_dir ./evaluation_results
```

#### 方式3: 使用shell脚本

```bash
# 确保在rtmonodepth环境中
conda activate rtmonodepth

# 运行脚本
./evaluate_rtmonoflow.sh
```

## 预期输出

成功运行后，您应该看到类似以下的输出：

```
RTMONOFLOW EVALUATION RESULTS
================================================================================
Model                     abs_rel  sq_rel   rmse     rmse_log a1       a2       a3       FPS     
--------------------------------------------------------------------------------
RTMonoFlow (adaptive)     0.XXX    0.XXX    X.XXX    0.XXX    0.XXX    0.XXX    0.XXX    XX.X
RTMonoDepth (Baseline)    0.XXX    0.XXX    X.XXX    0.XXX    0.XXX    0.XXX    0.XXX    XX.X

IMPROVEMENT ANALYSIS
============================================================
RTMonoFlow (adaptive):
  abs_rel improvement: +X.X%
  rmse improvement: +X.X%
  a1 improvement: +X.X%
  FPS change: +X.X%
```

## 故障排除

### 问题1: 环境未激活
```bash
# 解决方案
conda activate rtmonodepth
echo $CONDA_DEFAULT_ENV  # 验证
```

### 问题2: 缺少依赖
```bash
# 在rtmonodepth环境中安装
pip install scikit-image pillow tqdm
```

### 问题3: 模型权重未找到
```bash
# 检查可用模型
find ./log -name "weights_*" -type d
```

### 问题4: CUDA内存不足
```bash
# 减少批处理大小
python evaluate_flow.py ... --batch_size 4
```

## 验证步骤

### 1. 环境验证
```bash
conda activate rtmonodepth
python -c "
import torch
import cv2
import numpy as np
print('✓ All basic dependencies available')
print(f'PyTorch: {torch.__version__}')
print(f'CUDA: {torch.cuda.is_available()}')
"
```

### 2. 项目结构验证
```bash
python -c "
import os
files = ['evaluate_flow.py', 'options.py', 'datasets/__init__.py']
for f in files:
    print(f'✓ {f}' if os.path.exists(f) else f'✗ {f}')
"
```

### 3. 模型验证
```bash
python -c "
import os
model_path = './log/rtmonoflow_test/rtmonoflow_quick_test/models/weights_2'
baseline_path = './log/rtmonoflow_test/rtmonodepth_baseline_quick/models/weights_2'
print(f'RTMonoFlow model: {\"✓\" if os.path.exists(model_path) else \"✗\"} {model_path}')
print(f'Baseline model: {\"✓\" if os.path.exists(baseline_path) else \"✗\"} {baseline_path}')
"
```

## 下一步

评估完成后，您可以：

1. **查看结果**：检查控制台输出的精度指标
2. **分析预测**：查看保存在 `./evaluation_results/` 中的视差图
3. **比较性能**：对比RTMonoFlow和基线模型的改进
4. **可视化**：使用保存的预测结果进行进一步分析

## 联系支持

如果遇到问题，请提供：
1. 完整的错误信息
2. 当前的conda环境 (`echo $CONDA_DEFAULT_ENV`)
3. Python版本 (`python --version`)
4. 可用的模型路径 (`find ./log -name "weights_*"`)

祝评估顺利！
