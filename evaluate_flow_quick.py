#!/usr/bin/env python3
"""
Quick RTMonoFlow Evaluation Script
Simplified version for testing RTMonoFlow models without requiring trained weights
"""

import os
import sys
import cv2
import numpy as np
import torch
import time
from torch.utils.data import DataLoader
from tqdm import tqdm
import argparse

# Add RTMonoFlow to path
sys.path.append('networks')
sys.path.append('networks/RTMonoDepth')

from networks.RTMonoDepth.RTMonoDepth_s import DepthDecoder, DepthEncoder
from networks.RTMonoDepth.RTMonoFlow import RTMonoFlow, create_rtmonoflow

cv2.setNumThreads(0)


def create_dummy_dataset(num_samples=100, height=192, width=640):
    """Create dummy dataset for testing"""
    class DummyDataset:
        def __init__(self, num_samples, height, width):
            self.num_samples = num_samples
            self.height = height
            self.width = width
        
        def __len__(self):
            return self.num_samples
        
        def __getitem__(self, idx):
            # Create random image data
            image = torch.randn(3, self.height, self.width)
            return {("color", 0, 0): image}
    
    return DummyDataset(num_samples, height, width)


def evaluate_model_performance(model, dataloader, model_name="Model", device='cuda'):
    """Evaluate model performance metrics"""
    
    model.eval()
    inference_times = []
    memory_usage = []
    
    print(f"-> Evaluating {model_name} performance...")
    
    with torch.no_grad():
        for i, data in enumerate(tqdm(dataloader, desc=f"Testing {model_name}")):
            input_color = data[("color", 0, 0)].to(device)
            
            # Measure memory before inference
            if torch.cuda.is_available():
                torch.cuda.synchronize()
                memory_before = torch.cuda.memory_allocated() / 1024**2  # MB
            
            # Measure inference time
            start_time = time.time()
            
            if hasattr(model, 'forward'):
                # RTMonoFlow model
                output = model(input_color.unsqueeze(0))
            else:
                # Baseline model (encoder + decoder)
                encoder, decoder = model
                features = encoder(input_color.unsqueeze(0))
                output = decoder(features)
            
            if torch.cuda.is_available():
                torch.cuda.synchronize()
            
            end_time = time.time()
            inference_time = end_time - start_time
            inference_times.append(inference_time)
            
            # Measure memory after inference
            if torch.cuda.is_available():
                memory_after = torch.cuda.memory_allocated() / 1024**2  # MB
                memory_usage.append(memory_after - memory_before)
            
            # Only test a subset for quick evaluation
            if i >= 50:  # Test 50 samples
                break
    
    # Calculate statistics
    avg_inference_time = np.mean(inference_times)
    std_inference_time = np.std(inference_times)
    fps = 1.0 / avg_inference_time
    avg_memory = np.mean(memory_usage) if memory_usage else 0
    
    return {
        'avg_inference_time': avg_inference_time,
        'std_inference_time': std_inference_time,
        'fps': fps,
        'avg_memory_usage': avg_memory,
        'inference_times': inference_times
    }


def count_parameters(model):
    """Count model parameters"""
    if hasattr(model, 'parameters'):
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    else:
        # For tuple of models (encoder, decoder)
        encoder, decoder = model
        return sum(p.numel() for p in encoder.parameters() if p.requires_grad) + \
               sum(p.numel() for p in decoder.parameters() if p.requires_grad)


def get_model_size_mb(model):
    """Get model size in MB"""
    if hasattr(model, 'parameters'):
        param_size = sum(param.nelement() * param.element_size() for param in model.parameters())
        buffer_size = sum(buffer.nelement() * buffer.element_size() for buffer in model.buffers())
    else:
        # For tuple of models
        encoder, decoder = model
        param_size = sum(param.nelement() * param.element_size() for param in encoder.parameters()) + \
                    sum(param.nelement() * param.element_size() for param in decoder.parameters())
        buffer_size = sum(buffer.nelement() * buffer.element_size() for buffer in encoder.buffers()) + \
                     sum(buffer.nelement() * buffer.element_size() for buffer in decoder.buffers())
    
    return (param_size + buffer_size) / 1024**2


def test_output_quality(model, input_tensor, model_name="Model"):
    """Test output quality and characteristics"""

    if hasattr(model, 'eval'):
        model.eval()
    else:
        # For tuple of models
        encoder, decoder = model
        encoder.eval()
        decoder.eval()

    with torch.no_grad():
        if hasattr(model, 'forward'):
            output = model(input_tensor)
        else:
            encoder, decoder = model
            features = encoder(input_tensor)
            output = decoder(features)
    
    # Analyze output
    main_output = output[("disp", 0)]
    
    stats = {
        'output_shape': main_output.shape,
        'output_min': main_output.min().item(),
        'output_max': main_output.max().item(),
        'output_mean': main_output.mean().item(),
        'output_std': main_output.std().item(),
        'num_scales': len([k for k in output.keys() if k[0] == "disp"])
    }
    
    print(f"\n{model_name} Output Analysis:")
    print(f"  Shape: {stats['output_shape']}")
    print(f"  Range: [{stats['output_min']:.4f}, {stats['output_max']:.4f}]")
    print(f"  Mean: {stats['output_mean']:.4f}, Std: {stats['output_std']:.4f}")
    print(f"  Number of scales: {stats['num_scales']}")
    
    return stats


def print_comparison_table(results):
    """Print detailed comparison table"""
    
    print("\n" + "=" * 100)
    print("RTMONOFLOW QUICK EVALUATION RESULTS")
    print("=" * 100)
    
    # Header
    print(f"{'Model':<30} {'Params(M)':<10} {'Size(MB)':<10} {'FPS':<8} {'Time(ms)':<10} {'Memory(MB)':<12}")
    print("-" * 100)
    
    # Print results for each model
    for model_name, result in results.items():
        params_m = result['parameters'] / 1e6
        size_mb = result['model_size_mb']
        fps = result['performance']['fps']
        time_ms = result['performance']['avg_inference_time'] * 1000
        memory_mb = result['performance']['avg_memory_usage']
        
        print(f"{model_name:<30} {params_m:<10.2f} {size_mb:<10.2f} {fps:<8.1f} {time_ms:<10.2f} {memory_mb:<12.1f}")
    
    # Print relative comparison
    if len(results) > 1:
        print("\n" + "=" * 80)
        print("RELATIVE COMPARISON (vs RTMonoDepth Baseline)")
        print("=" * 80)
        
        baseline_key = None
        for key in results.keys():
            if 'Baseline' in key or 'RTMonoDepth' in key:
                baseline_key = key
                break
        
        if baseline_key:
            baseline = results[baseline_key]
            
            for model_name, result in results.items():
                if model_name == baseline_key:
                    continue
                
                param_ratio = result['parameters'] / baseline['parameters']
                fps_ratio = result['performance']['fps'] / baseline['performance']['fps']
                size_ratio = result['model_size_mb'] / baseline['model_size_mb']
                
                print(f"\n{model_name}:")
                print(f"  Parameter ratio: {param_ratio:.2f}x")
                print(f"  FPS ratio: {fps_ratio:.2f}x")
                print(f"  Size ratio: {size_ratio:.2f}x")
                
                # Performance per parameter
                baseline_perf_per_param = baseline['performance']['fps'] / (baseline['parameters'] / 1e6)
                current_perf_per_param = result['performance']['fps'] / (result['parameters'] / 1e6)
                efficiency_ratio = current_perf_per_param / baseline_perf_per_param
                
                print(f"  Efficiency ratio: {efficiency_ratio:.2f}x")


def main():
    parser = argparse.ArgumentParser(description='Quick RTMonoFlow evaluation')
    
    parser.add_argument('--height', type=int, default=192, help='Input height')
    parser.add_argument('--width', type=int, default=640, help='Input width')
    parser.add_argument('--batch_size', type=int, default=1, help='Batch size')
    parser.add_argument('--num_samples', type=int, default=100, help='Number of test samples')
    parser.add_argument('--cpu', action='store_true', help='Use CPU instead of GPU')
    parser.add_argument('--test_all_variants', action='store_true', 
                       help='Test all RTMonoFlow variants')
    
    args = parser.parse_args()
    
    # Setup device
    if args.cpu or not torch.cuda.is_available():
        device = torch.device('cpu')
        print("Using CPU for evaluation")
    else:
        device = torch.device('cuda')
        print(f"Using GPU: {torch.cuda.get_device_name()}")
    
    print(f"Input size: {args.height}x{args.width}")
    print(f"Batch size: {args.batch_size}")
    print(f"Test samples: {args.num_samples}")
    
    # Create dummy dataset
    dataset = create_dummy_dataset(args.num_samples, args.height, args.width)
    dataloader = DataLoader(dataset, batch_size=1, shuffle=False, num_workers=0)
    
    # Create test input
    test_input = torch.randn(args.batch_size, 3, args.height, args.width).to(device)
    
    # Models to test
    models_to_test = {}
    
    # Baseline RTMonoDepth
    print("\n-> Creating RTMonoDepth (Baseline)...")
    encoder = DepthEncoder().to(device)
    decoder = DepthDecoder(encoder.num_ch_enc).to(device)
    models_to_test["RTMonoDepth (Baseline)"] = (encoder, decoder)
    
    if args.test_all_variants:
        # All RTMonoFlow variants
        print("-> Creating RTMonoFlow variants...")
        
        models_to_test["RTMonoFlow (Adaptive HEEP)"] = create_rtmonoflow(
            use_adaptive_heep=True, use_flow_blocks=True
        ).to(device)
        
        models_to_test["RTMonoFlow (Standard HEEP)"] = create_rtmonoflow(
            use_adaptive_heep=False, use_flow_blocks=True
        ).to(device)
        
        models_to_test["RTMonoFlow (No Flow Blocks)"] = create_rtmonoflow(
            use_adaptive_heep=True, use_flow_blocks=False
        ).to(device)
    else:
        # Just the recommended variant
        print("-> Creating RTMonoFlow (Adaptive HEEP)...")
        models_to_test["RTMonoFlow (Adaptive HEEP)"] = create_rtmonoflow(
            use_adaptive_heep=True, use_flow_blocks=True
        ).to(device)
    
    # Evaluate all models
    results = {}
    
    for model_name, model in models_to_test.items():
        print(f"\n{'='*60}")
        print(f"EVALUATING: {model_name}")
        print(f"{'='*60}")
        
        # Count parameters and model size
        params = count_parameters(model)
        model_size = get_model_size_mb(model)
        
        print(f"Parameters: {params:,} ({params/1e6:.2f}M)")
        print(f"Model size: {model_size:.2f} MB")
        
        # Test output quality
        output_stats = test_output_quality(model, test_input, model_name)
        
        # Evaluate performance
        performance = evaluate_model_performance(model, dataloader, model_name, device)
        
        # Store results
        results[model_name] = {
            'parameters': params,
            'model_size_mb': model_size,
            'performance': performance,
            'output_stats': output_stats
        }
        
        print(f"Average inference time: {performance['avg_inference_time']*1000:.2f} ± {performance['std_inference_time']*1000:.2f} ms")
        print(f"FPS: {performance['fps']:.1f}")
        if performance['avg_memory_usage'] > 0:
            print(f"Average memory usage: {performance['avg_memory_usage']:.1f} MB")
    
    # Print comparison
    print_comparison_table(results)
    
    # Print recommendations
    print("\n" + "=" * 80)
    print("RECOMMENDATIONS")
    print("=" * 80)
    
    best_fps = max(results.items(), key=lambda x: x[1]['performance']['fps'])
    most_efficient = max(results.items(), key=lambda x: x[1]['performance']['fps'] / (x[1]['parameters'] / 1e6))
    
    print(f"🚀 Fastest model: {best_fps[0]} ({best_fps[1]['performance']['fps']:.1f} FPS)")
    print(f"⚡ Most efficient: {most_efficient[0]} ({most_efficient[1]['performance']['fps'] / (most_efficient[1]['parameters'] / 1e6):.1f} FPS/M params)")
    
    if len(results) > 1:
        print(f"\n💡 RTMonoFlow provides enhanced feature extraction capabilities")
        print(f"   at the cost of increased parameters and slightly reduced speed.")
        print(f"   Choose based on your accuracy vs speed requirements.")
    
    print("\n-> Quick evaluation completed!")


if __name__ == "__main__":
    main()
