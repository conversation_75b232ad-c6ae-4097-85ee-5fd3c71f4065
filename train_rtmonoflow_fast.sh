#!/bin/bash

# RTMonoFlow Fast Training Script
# 专门针对FPS优化的训练配置

echo "RTMonoFlow Fast Training (FPS Optimized)"
echo "========================================"

# Set environment variables
export CUDA_VISIBLE_DEVICES=0

# Fast Configuration 1: Standard HEEP + Reduced Scales
echo "Fast Config 1: Standard HEEP + Reduced Scales"
python train.py \
--model_name rtmonoflow_fast_v1 \
--use_rtmonoflow \
--use_flow_blocks \
--heep_pyramid_levels 2 \
--num_epochs 20 \
--learning_rate 1e-4 \
--log_dir ./log/rtmonoflow_fast \
--data_path /mnt/acer/kitti_jpg \
--num_workers 12 \
--batch_size 8 \
--scales 0 1 2 \
--height 192 \
--width 640 \
--disparity_smoothness 1e-3 \
--frame_ids 0 -1 1 \
--use_stereo

echo "Fast Config 1 completed!"

# Fast Configuration 1.5: Fine-tuning
echo "Fast Config 1.5: Fine-tuning Standard HEEP"
python train.py \
--model_name rtmonoflow_fast_v1_finetune \
--load_weights_folder ./log/rtmonoflow_fast/rtmonoflow_fast_v1/models/weights_19 \
--use_rtmonoflow \
--use_flow_blocks \
--heep_pyramid_levels 2 \
--num_epochs 10 \
--learning_rate 5e-5 \
--log_dir ./log/rtmonoflow_fast \
--data_path /mnt/acer/kitti_jpg \
--num_workers 12 \
--batch_size 8 \
--scales 0 1 2 \
--height 192 \
--width 640 \
--disparity_smoothness 1e-3 \
--frame_ids 0 -1 1 \
--use_stereo

echo "Fast Config 1.5 completed!"

# Fast Configuration 2: Minimal HEEP + Single Scale
echo "Fast Config 2: Minimal HEEP + Single Scale"
python train.py \
--model_name rtmonoflow_fast_v2 \
--use_rtmonoflow \
--use_flow_blocks \
--heep_pyramid_levels 2 \
--num_epochs 20 \
--learning_rate 1e-4 \
--log_dir ./log/rtmonoflow_fast \
--data_path /mnt/acer/kitti_jpg \
--num_workers 12 \
--batch_size 8 \
--scales 0 1 \
--height 192 \
--width 640 \
--disparity_smoothness 1e-3 \
--frame_ids 0 -1 1 \
--use_stereo

echo "Fast Config 2 completed!"

# Fast Configuration 2.5: Fine-tuning
echo "Fast Config 2.5: Fine-tuning Minimal HEEP"
python train.py \
--model_name rtmonoflow_fast_v2_finetune \
--load_weights_folder ./log/rtmonoflow_fast/rtmonoflow_fast_v2/models/weights_19 \
--use_rtmonoflow \
--use_flow_blocks \
--heep_pyramid_levels 2 \
--num_epochs 10 \
--learning_rate 5e-5 \
--log_dir ./log/rtmonoflow_fast \
--data_path /mnt/acer/kitti_jpg \
--num_workers 12 \
--batch_size 8 \
--scales 0 1 \
--height 192 \
--width 640 \
--disparity_smoothness 1e-3 \
--frame_ids 0 -1 1 \
--use_stereo

echo "Fast Config 2.5 completed!"

# Fast Configuration 3: Ultra Fast (No HEEP)
echo "Fast Config 3: Ultra Fast (No RTMonoFlow features)"
python train.py \
--model_name rtmonoflow_ultra_fast \
--num_epochs 20 \
--learning_rate 1e-4 \
--log_dir ./log/rtmonoflow_fast \
--data_path /mnt/acer/kitti_jpg \
--num_workers 12 \
--batch_size 8 \
--scales 0 1 \
--height 192 \
--width 640 \
--disparity_smoothness 1e-3 \
--frame_ids 0 -1 1 \
--use_stereo

echo "Fast Config 3 completed!"

echo "All fast training configurations completed!"
echo "=========================================="
echo "Fast Training Summary:"
echo "- Fast V1 (Standard HEEP, 3 scales): ./log/rtmonoflow_fast/rtmonoflow_fast_v1_finetune"
echo "- Fast V2 (Minimal HEEP, 2 scales): ./log/rtmonoflow_fast/rtmonoflow_fast_v2_finetune"  
echo "- Ultra Fast (No HEEP, 2 scales): ./log/rtmonoflow_fast/rtmonoflow_ultra_fast"
echo "=========================================="

# Run FPS evaluation
echo "Running FPS evaluation on fast models..."

# Evaluate Fast V1
echo "Evaluating Fast V1..."
python evaluate_flow.py \
--load_weights_folder ./log/rtmonoflow_fast/rtmonoflow_fast_v1_finetune/models/weights_9 \
--model_type standard \
--eval_mono \
--data_path /mnt/acer/kitti_jpg \
--eval_split eigen \
--baseline_weights ./log/rtmonoflow_fast/rtmonoflow_ultra_fast/models/weights_19 \
--num_workers 4 \
--batch_size 16

# Evaluate Fast V2  
echo "Evaluating Fast V2..."
python evaluate_flow.py \
--load_weights_folder ./log/rtmonoflow_fast/rtmonoflow_fast_v2_finetune/models/weights_9 \
--model_type standard \
--eval_mono \
--data_path /mnt/acer/kitti_jpg \
--eval_split eigen \
--baseline_weights ./log/rtmonoflow_fast/rtmonoflow_ultra_fast/models/weights_19 \
--num_workers 4 \
--batch_size 16

echo "FPS evaluation completed!"
echo "Check the results to see speed vs accuracy trade-offs."
