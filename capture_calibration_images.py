#!/usr/bin/env python3
"""
简单的CSI摄像头拍照工具
用于手动拍摄标定图片
"""

import cv2
import os
import time

def get_csi_camera_pipeline(camera_id=0, width=1280, height=720, fps=30):
    """构建CSI摄像头的GStreamer管道"""
    return (
        f"nvarguscamerasrc sensor-id={camera_id} ! "
        f"video/x-raw(memory:NVMM), width={width}, height={height}, "
        f"format=NV12, framerate={fps}/1 ! "
        f"nvvidconv flip-method=0 ! "
        f"video/x-raw, width={width}, height={height}, format=BGRx ! "
        f"videoconvert ! "
        f"video/x-raw, format=BGR ! appsink"
    )

def capture_images():
    """拍摄标定图片"""
    # 创建保存目录
    os.makedirs('calib_images', exist_ok=True)
    
    print("📷 CSI摄像头拍照工具")
    print("=" * 30)
    print("操作说明:")
    print("- 按 SPACE 键拍照")
    print("- 按 'q' 退出")
    print("- 建议拍摄20-30张不同角度的棋盘格图片")
    print()
    
    # 打开CSI摄像头
    gst_pipeline = get_csi_camera_pipeline(0, 1280, 720, 30)
    cap = cv2.VideoCapture(gst_pipeline, cv2.CAP_GSTREAMER)
    
    if not cap.isOpened():
        print("❌ 无法打开CSI摄像头")
        print("请检查摄像头连接")
        return False
    
    print("✅ CSI摄像头已打开")
    
    image_count = 0
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                print("❌ 无法读取摄像头帧")
                break
            
            # 在图像上显示信息
            info_text = f"已拍摄: {image_count} 张 | 按SPACE拍照, 按Q退出"
            cv2.putText(frame, info_text, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # 显示图像（如果支持GUI）
            try:
                cv2.imshow('CSI Camera - Press SPACE to capture', frame)
                key = cv2.waitKey(1) & 0xFF
            except cv2.error:
                # 如果无法显示窗口，每3秒自动拍一张
                print(f"无法显示窗口，自动拍摄第 {image_count + 1} 张...")
                key = ord(' ')
                time.sleep(3)
                if image_count >= 25:  # 自动拍摄25张后退出
                    break
            
            if key == ord(' '):
                # 拍照
                filename = f"calib_images/calib_{image_count:03d}.jpg"
                cv2.imwrite(filename, frame)
                print(f"✅ 已保存: {filename}")
                image_count += 1
                
            elif key == ord('q'):
                break
    
    except KeyboardInterrupt:
        print("\n用户中断拍摄")
    
    finally:
        cap.release()
        cv2.destroyAllWindows()
    
    print(f"\n📸 拍摄完成，共拍摄 {image_count} 张图片")
    return image_count > 0

if __name__ == "__main__":
    success = capture_images()
    if success:
        print("\n下一步：运行标定程序")
        print("python3 downsetup/getcameraparameter.py --images 'calib_images/*.jpg'")
    else:
        print("拍摄失败")
