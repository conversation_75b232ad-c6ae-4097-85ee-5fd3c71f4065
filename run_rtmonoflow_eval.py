#!/usr/bin/env python3
"""
Simple RTMonoFlow evaluation runner
Assumes conda environment is already activated
"""

import os
import sys
import subprocess

def check_environment():
    """Check if we're in the right environment"""
    print("Checking environment...")
    
    # Check if we're in conda environment
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', 'None')
    print(f"Current conda environment: {conda_env}")
    
    if conda_env != 'rtmonodepth':
        print("Warning: Not in rtmonodepth environment")
        print("Please run: conda activate rtmonodepth")
        return False
    
    # Check basic imports
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__} available")
        print(f"✓ CUDA available: {torch.cuda.is_available()}")
    except ImportError:
        print("✗ PyTorch not available")
        return False
    
    try:
        import cv2
        print(f"✓ OpenCV {cv2.__version__} available")
    except ImportError:
        print("✗ OpenCV not available")
        return False
    
    return True

def find_available_models():
    """Find available model weights"""
    print("\nSearching for available models...")
    
    model_paths = []
    baseline_paths = []
    
    # Search for RTMonoFlow models
    for root, dirs, files in os.walk('./log'):
        if 'weights_2' in dirs:
            weights_path = os.path.join(root, 'weights_2')
            if 'rtmonoflow' in root.lower():
                model_paths.append(weights_path)
            elif 'baseline' in root.lower():
                baseline_paths.append(weights_path)
    
    print(f"Found {len(model_paths)} RTMonoFlow model(s):")
    for path in model_paths:
        print(f"  - {path}")
    
    print(f"Found {len(baseline_paths)} baseline model(s):")
    for path in baseline_paths:
        print(f"  - {path}")
    
    return model_paths, baseline_paths

def run_evaluation(model_path, baseline_path, data_path="/mnt/acer/kitti_jpg"):
    """Run the evaluation"""
    print(f"\nRunning evaluation...")
    print(f"Model: {model_path}")
    print(f"Baseline: {baseline_path}")
    print(f"Data: {data_path}")
    
    cmd = [
        "python", "evaluate_flow.py",
        "--load_weights_folder", model_path,
        "--model_type", "adaptive",
        "--eval_mono",
        "--data_path", data_path,
        "--eval_split", "eigen",
        "--baseline_weights", baseline_path,
        "--num_workers", "4",
        "--batch_size", "8",
        "--save_pred_disps",
        "--output_dir", "./evaluation_results"
    ]
    
    print(f"Command: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print("✓ Evaluation completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Evaluation failed with return code {e.returncode}")
        return False
    except Exception as e:
        print(f"✗ Evaluation failed: {e}")
        return False

def main():
    print("RTMonoFlow Evaluation Runner")
    print("=" * 40)
    
    # Check environment
    if not check_environment():
        print("\nPlease fix environment issues and try again.")
        return 1
    
    # Find models
    model_paths, baseline_paths = find_available_models()
    
    if not model_paths:
        print("\nNo RTMonoFlow models found!")
        print("Please train a model first or check the log directory.")
        return 1
    
    if not baseline_paths:
        print("\nNo baseline models found!")
        print("Using first available model as baseline...")
        baseline_paths = model_paths
    
    # Use the first available models
    model_path = model_paths[0]
    baseline_path = baseline_paths[0]
    
    # Check data path
    data_path = "/mnt/acer/kitti_jpg"
    if not os.path.exists(data_path):
        print(f"\nData path not found: {data_path}")
        print("Please provide the correct KITTI dataset path.")
        return 1
    
    # Run evaluation
    success = run_evaluation(model_path, baseline_path, data_path)
    
    if success:
        print("\n" + "=" * 40)
        print("Evaluation completed successfully!")
        print("Check the output above for results.")
        print("Predictions saved in: ./evaluation_results/")
        return 0
    else:
        print("\n" + "=" * 40)
        print("Evaluation failed!")
        print("Please check the error messages above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
