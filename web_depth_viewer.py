#!/usr/bin/env python3
"""
基于Web的深度估计查看器
在开发板上运行，通过浏览器查看结果
"""

import cv2
import numpy as np
import base64
import time
import threading
from flask import Flask, render_template_string, Response
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from downsetup.jetson_deployment import JetsonDepthEstimator

app = Flask(__name__)

class WebDepthEstimator:
    def __init__(self):
        self.latest_frame = None
        self.latest_depth = None
        self.fps = 0
        self.running = False
        
    def get_csi_camera_pipeline(self, camera_id=0, width=1280, height=720, fps=30):
        """构建CSI摄像头的GStreamer管道"""
        return (
            f"nvarguscamerasrc sensor-id={camera_id} ! "
            f"video/x-raw(memory:NVMM), width={width}, height={height}, "
            f"format=NV12, framerate={fps}/1 ! "
            f"nvvidconv flip-method=0 ! "
            f"video/x-raw, width={width}, height={height}, format=BGRx ! "
            f"videoconvert ! "
            f"video/x-raw, format=BGR ! appsink"
        )
    
    def start_camera_thread(self, estimator):
        """启动摄像头线程"""
        self.running = True
        
        # 打开CSI摄像头
        gst_pipeline = self.get_csi_camera_pipeline(0, 1280, 720, 30)
        cap = cv2.VideoCapture(gst_pipeline, cv2.CAP_GSTREAMER)
        
        if not cap.isOpened():
            print("❌ 无法打开CSI摄像头")
            return
        
        print("✅ 摄像头已打开，开始处理...")
        
        fps_history = []
        frame_count = 0
        
        while self.running:
            ret, frame = cap.read()
            if not ret:
                continue
            
            # 深度估计
            start_time = time.time()
            depth_map, inference_time = estimator.predict_depth(frame)
            
            # 计算FPS
            fps = 1.0 / inference_time
            fps_history.append(fps)
            if len(fps_history) > 30:
                fps_history.pop(0)
            self.fps = np.mean(fps_history)
            
            # 深度图可视化
            depth_normalized = (depth_map - depth_map.min()) / (depth_map.max() - depth_map.min())
            depth_colored = cv2.applyColorMap(
                (depth_normalized * 255).astype(np.uint8),
                cv2.COLORMAP_JET
            )
            
            # 调整深度图大小
            original_h, original_w = frame.shape[:2]
            depth_colored = cv2.resize(depth_colored, (original_w, original_h))
            
            # 添加信息文本
            info_text = [
                f"FPS: {self.fps:.1f}",
                f"推理时间: {inference_time*1000:.1f}ms",
                f"帧数: {frame_count}",
                f"分辨率: {original_w}x{original_h}"
            ]
            
            # 在原图上添加文本
            y_offset = 30
            for text in info_text:
                cv2.putText(frame, text, (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                y_offset += 25
            
            # 在深度图上添加标题
            cv2.putText(depth_colored, "Depth Map", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            # 更新最新帧
            self.latest_frame = frame.copy()
            self.latest_depth = depth_colored.copy()
            
            frame_count += 1
            
            # 控制帧率
            time.sleep(0.01)
        
        cap.release()
        print("摄像头线程结束")
    
    def get_frame_jpeg(self, frame_type='original'):
        """获取JPEG编码的帧"""
        if frame_type == 'original' and self.latest_frame is not None:
            frame = self.latest_frame
        elif frame_type == 'depth' and self.latest_depth is not None:
            frame = self.latest_depth
        else:
            # 返回黑色图像
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            cv2.putText(frame, "No Image", (250, 240),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        # 编码为JPEG
        ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
        if ret:
            return buffer.tobytes()
        return None

# 全局变量
web_estimator = WebDepthEstimator()
depth_estimator = None

@app.route('/')
def index():
    """主页面"""
    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>RT-MonoDepth 实时深度估计</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background-color: #f0f0f0; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { text-align: center; margin-bottom: 20px; }
            .video-container { display: flex; justify-content: space-around; flex-wrap: wrap; }
            .video-box { margin: 10px; text-align: center; background: white; padding: 10px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .video-box h3 { margin-top: 0; color: #333; }
            img { max-width: 100%; height: auto; border: 2px solid #ddd; border-radius: 4px; }
            .info { background: white; padding: 15px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .status { color: #28a745; font-weight: bold; }
        </style>
        <script>
            function refreshImages() {
                document.getElementById('original').src = '/video_feed/original?' + new Date().getTime();
                document.getElementById('depth').src = '/video_feed/depth?' + new Date().getTime();
            }
            setInterval(refreshImages, 100); // 10 FPS
        </script>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 RT-MonoDepth 实时深度估计</h1>
                <p class="status">✅ 系统运行中 - FPS: <span id="fps">{{ fps }}</span></p>
            </div>
            
            <div class="video-container">
                <div class="video-box">
                    <h3>📷 原始图像</h3>
                    <img id="original" src="/video_feed/original" alt="原始图像">
                </div>
                <div class="video-box">
                    <h3>🎯 深度图</h3>
                    <img id="depth" src="/video_feed/depth" alt="深度图">
                </div>
            </div>
            
            <div class="info">
                <h3>📊 系统信息</h3>
                <p><strong>模型类型:</strong> RTMonoDepth</p>
                <p><strong>输入分辨率:</strong> 1280x720</p>
                <p><strong>处理分辨率:</strong> 640x192</p>
                <p><strong>摄像头:</strong> CSI Camera</p>
                <p><strong>访问地址:</strong> http://开发板IP:5000</p>
            </div>
            
            <div class="info">
                <h3>🎮 操作说明</h3>
                <p>• 图像会自动刷新显示实时结果</p>
                <p>• 深度图中红色表示近距离，蓝色表示远距离</p>
                <p>• 可以在多个设备上同时访问此页面</p>
            </div>
        </div>
    </body>
    </html>
    """
    return render_template_string(html_template, fps=web_estimator.fps)

@app.route('/video_feed/<frame_type>')
def video_feed(frame_type):
    """视频流"""
    def generate():
        while True:
            frame_data = web_estimator.get_frame_jpeg(frame_type)
            if frame_data:
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_data + b'\r\n')
            time.sleep(0.1)  # 10 FPS
    
    return Response(generate(), mimetype='multipart/x-mixed-replace; boundary=frame')

def main():
    global depth_estimator
    
    print("🚀 启动Web深度估计查看器...")
    
    # 模拟命令行参数
    class Args:
        model_type = 'small'
        weights_folder = 'weights/RTMonoDepth/s/m_640_192/'
        camera_params = 'my_camera_params.txt'
        input_height = 192
        input_width = 640
        use_tensorrt = True
        force_convert = False
        benchmark_cycles = 200
        camera_test = True
        camera_id = 0
        use_csi = True
    
    args = Args()
    
    # 创建深度估计器
    depth_estimator = JetsonDepthEstimator(args)
    
    # 启动摄像头线程
    camera_thread = threading.Thread(target=web_estimator.start_camera_thread, args=(depth_estimator,))
    camera_thread.daemon = True
    camera_thread.start()
    
    print("✅ 深度估计器已启动")
    print("🌐 Web服务器启动中...")
    print("📱 请在浏览器中访问: http://开发板IP:5000")
    print("   例如: http://************:5000")
    
    # 启动Web服务器
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        web_estimator.running = False
        print("\n程序已停止")
