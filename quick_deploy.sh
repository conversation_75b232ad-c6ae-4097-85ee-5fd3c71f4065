#!/bin/bash
# RT-MonoDepth Orin NX 快速部署脚本

set -e  # 遇到错误立即退出

echo "🚀 RT-MonoDepth Orin NX 快速部署工具"
echo "======================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查是否在Jetson设备上
check_jetson() {
    if [ ! -f /etc/nv_tegra_release ]; then
        echo -e "${RED}❌ 错误：此脚本只能在NVIDIA Jetson设备上运行${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 检测到Jetson设备${NC}"
}

# 检查权重文件
check_weights() {
    local weights_dir="weights/RTMonoDepth/s/m_640_192"
    if [ ! -f "$weights_dir/encoder.pth" ] || [ ! -f "$weights_dir/depth.pth" ]; then
        echo -e "${RED}❌ 错误：未找到权重文件${NC}"
        echo "请确保以下文件存在："
        echo "  - $weights_dir/encoder.pth"
        echo "  - $weights_dir/depth.pth"
        exit 1
    fi
    echo -e "${GREEN}✅ 权重文件检查通过${NC}"
}

# 检查Python环境
check_python_env() {
    echo "🔍 检查Python环境..."
    
    # 检查PyTorch
    if ! python3 -c "import torch" 2>/dev/null; then
        echo -e "${RED}❌ PyTorch未安装${NC}"
        echo "请运行: ./setup_orin_nx_environment.sh"
        exit 1
    fi
    
    # 检查torch2trt
    if ! python3 -c "import torch2trt" 2>/dev/null; then
        echo -e "${YELLOW}⚠️ torch2trt未安装，将禁用TensorRT优化${NC}"
        USE_TENSORRT=""
    else
        USE_TENSORRT="--use_tensorrt"
        echo -e "${GREEN}✅ torch2trt可用，将启用TensorRT优化${NC}"
    fi
    
    echo -e "${GREEN}✅ Python环境检查通过${NC}"
}

# 设置性能模式
set_performance_mode() {
    echo "⚡ 设置最大性能模式..."
    sudo nvpmodel -m 0 2>/dev/null || echo -e "${YELLOW}⚠️ 无法设置nvpmodel${NC}"
    sudo jetson_clocks 2>/dev/null || echo -e "${YELLOW}⚠️ 无法设置jetson_clocks${NC}"
    echo -e "${GREEN}✅ 性能模式设置完成${NC}"
}

# 相机标定
camera_calibration() {
    echo ""
    echo "📷 相机标定"
    echo "============"
    
    if [ -f "my_camera_params.txt" ]; then
        echo -e "${GREEN}✅ 发现已有相机参数文件: my_camera_params.txt${NC}"
        read -p "是否重新标定？(y/N): " recalibrate
        if [[ $recalibrate =~ ^[Yy]$ ]]; then
            perform_calibration
        fi
    else
        echo "未找到相机参数文件，需要进行标定"
        perform_calibration
    fi
}

perform_calibration() {
    echo "请选择摄像头类型："
    echo "1) CSI摄像头（板载摄像头）"
    echo "2) USB摄像头"

    read -p "请选择摄像头类型 (1-2): " cam_type

    local csi_flag=""
    case $cam_type in
        1)
            csi_flag="--csi"
            echo -e "${GREEN}✅ 选择CSI摄像头${NC}"
            ;;
        2)
            echo -e "${GREEN}✅ 选择USB摄像头${NC}"
            ;;
        *)
            echo "无效选择，默认使用USB摄像头"
            ;;
    esac

    echo "请选择标定方式："
    echo "1) 实时标定（推荐）"
    echo "2) 使用已有图片"
    echo "3) 跳过标定（使用KITTI默认参数）"

    read -p "请选择 (1-3): " choice

    case $choice in
        1)
            echo "开始实时标定..."
            echo "请准备9x6棋盘格标定板（方格大小25mm）"
            echo "操作说明："
            echo "  - 将标定板放在摄像头前"
            echo "  - 当检测到棋盘格时按空格键捕获"
            echo "  - 从不同角度和距离捕获20张图片"
            echo "  - 按'q'退出"
            read -p "按Enter继续..."
            python3 downsetup/getcameraparameter.py --live --camera_id 0 --num_images 20 $csi_flag
            ;;
        2)
            echo "使用已有图片进行标定..."
            python3 downsetup/getcameraparameter.py --images "calib_images/*.jpg"
            ;;
        3)
            echo "跳过标定，将使用KITTI默认参数"
            return
            ;;
        *)
            echo "无效选择，跳过标定"
            return
            ;;
    esac
}

# 性能测试
performance_test() {
    echo ""
    echo "📊 性能测试"
    echo "==========="
    
    local camera_params=""
    if [ -f "my_camera_params.txt" ]; then
        camera_params="--camera_params my_camera_params.txt"
    fi
    
    echo "开始性能测试..."
    python3 downsetup/jetson_deployment.py \
        --model_type small \
        --weights_folder weights/RTMonoDepth/s/m_640_192/ \
        $camera_params \
        $USE_TENSORRT \
        --benchmark_cycles 100
}

# 实时测试
real_time_test() {
    echo ""
    echo "🎥 实时深度估计测试"
    echo "=================="

    local camera_params=""
    if [ -f "my_camera_params.txt" ]; then
        camera_params="--camera_params my_camera_params.txt"
        echo -e "${GREEN}✅ 使用自定义相机参数${NC}"
    else
        echo -e "${YELLOW}⚠️ 使用KITTI默认参数${NC}"
    fi

    echo "请选择摄像头类型："
    echo "1) CSI摄像头（板载摄像头）"
    echo "2) USB摄像头"

    read -p "请选择摄像头类型 (1-2): " cam_type

    local csi_flag=""
    case $cam_type in
        1)
            csi_flag="--use_csi"
            echo -e "${GREEN}✅ 使用CSI摄像头${NC}"
            ;;
        2)
            echo -e "${GREEN}✅ 使用USB摄像头${NC}"
            ;;
        *)
            echo "无效选择，默认使用USB摄像头"
            ;;
    esac

    echo "启动实时深度估计..."
    echo "操作说明："
    echo "  - 按 'q' 退出"
    echo "  - 按 's' 保存当前帧"
    echo ""
    read -p "按Enter开始..."

    python3 downsetup/jetson_deployment.py \
        --model_type small \
        --weights_folder weights/RTMonoDepth/s/m_640_192/ \
        $camera_params \
        $USE_TENSORRT \
        --camera_test \
        --camera_id 0 \
        $csi_flag
}

# 主菜单
main_menu() {
    while true; do
        echo ""
        echo "🎯 请选择操作："
        echo "==============="
        echo "1) 相机标定"
        echo "2) 性能测试"
        echo "3) 实时深度估计"
        echo "4) 完整流程（标定+测试+实时）"
        echo "5) 退出"
        echo ""
        read -p "请选择 (1-5): " choice
        
        case $choice in
            1)
                camera_calibration
                ;;
            2)
                performance_test
                ;;
            3)
                real_time_test
                ;;
            4)
                camera_calibration
                performance_test
                real_time_test
                ;;
            5)
                echo "👋 再见！"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重试${NC}"
                ;;
        esac
    done
}

# 主程序
main() {
    check_jetson
    check_weights
    check_python_env
    set_performance_mode
    
    echo ""
    echo -e "${GREEN}🎉 环境检查完成，准备就绪！${NC}"
    
    main_menu
}

# 运行主程序
main "$@"
