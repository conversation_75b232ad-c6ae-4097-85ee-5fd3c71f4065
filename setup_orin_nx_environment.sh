#!/bin/bash
# RT-MonoDepth Orin NX环境配置脚本

echo "🚀 开始配置Orin NX环境..."

# 1. 更新系统
sudo apt update && sudo apt upgrade -y

# 2. 安装基础依赖
sudo apt install -y python3-pip python3-dev cmake build-essential
sudo apt install -y libopencv-dev python3-opencv
sudo apt install -y libhdf5-dev libhdf5-serial-dev

# 3. 安装PyTorch for Jetson (根据JetPack版本选择)
# JetPack 5.1.2 (推荐)
wget https://developer.download.nvidia.com/compute/redist/jp/v512/pytorch/torch-2.0.0+nv23.05-cp38-cp38-linux_aarch64.whl
pip3 install torch-2.0.0+nv23.05-cp38-cp38-linux_aarch64.whl

# 4. 安装torchvision
sudo apt install -y libjpeg-dev zlib1g-dev libpython3-dev libavcodec-dev libavformat-dev libswscale-dev
git clone --branch v0.15.1 https://github.com/pytorch/vision torchvision
cd torchvision
export BUILD_VERSION=0.15.1
python3 setup.py install --user
cd ..

# 5. 安装torch2trt
git clone https://github.com/NVIDIA-AI-IOT/torch2trt
cd torch2trt
python3 setup.py install --user
cd ..

# 6. 安装其他依赖
pip3 install numpy opencv-python pillow matplotlib

# 7. 增加SWAP内存 (TensorRT转换需要)
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab

# 8. 设置GPU性能模式
sudo nvpmodel -m 0  # 最大性能模式
sudo jetson_clocks   # 锁定最高频率

echo "✅ 环境配置完成！"
echo "请重启系统后继续下一步。"
