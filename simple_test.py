#!/usr/bin/env python3
"""
Simple test to check if basic modules are available
"""

print("Testing basic imports...")

try:
    import sys
    print(f"✓ Python version: {sys.version}")
except:
    print("✗ Python import failed")

try:
    import os
    print("✓ OS module available")
except:
    print("✗ OS module failed")

try:
    import numpy as np
    print(f"✓ NumPy version: {np.__version__}")
except ImportError:
    print("✗ NumPy not available")

try:
    import torch
    print(f"✓ PyTorch version: {torch.__version__}")
    print(f"✓ CUDA available: {torch.cuda.is_available()}")
except ImportError:
    print("✗ PyTorch not available")

try:
    import cv2
    print(f"✓ OpenCV version: {cv2.__version__}")
except ImportError:
    print("✗ OpenCV not available")

print("\nChecking project structure...")

# Check if key files exist
files_to_check = [
    "evaluate_depth_full.py",
    "options.py", 
    "datasets/__init__.py",
    "networks/RTMonoDepth/RTMonoDepth.py",
    "networks/RTMonoDepth/RTMonoFlow.py"
]

for file_path in files_to_check:
    if os.path.exists(file_path):
        print(f"✓ {file_path} exists")
    else:
        print(f"✗ {file_path} missing")

print("\nTo run RTMonoFlow evaluation:")
print("1. Activate conda environment: conda activate rtmonodepth")
print("2. Run: python evaluate_flow.py --help")
print("3. Or use the shell script: ./evaluate_rtmonoflow.sh")
