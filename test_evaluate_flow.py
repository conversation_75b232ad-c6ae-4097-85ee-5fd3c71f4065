#!/usr/bin/env python3
"""
Test script for RTMonoFlow evaluation
Verifies that the evaluation environment is set up correctly
"""

import os
import sys
import torch
import numpy as np

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import cv2
        print("✓ OpenCV imported successfully")
    except ImportError as e:
        print(f"✗ OpenCV import failed: {e}")
        return False
    
    try:
        import datasets
        print("✓ Datasets module imported successfully")
    except ImportError as e:
        print(f"✗ Datasets import failed: {e}")
        return False
    
    try:
        from layers import disp_to_depth
        print("✓ Layers module imported successfully")
    except ImportError as e:
        print(f"✗ Layers import failed: {e}")
        return False
    
    try:
        from options import MonodepthOptions
        print("✓ Options module imported successfully")
    except ImportError as e:
        print(f"✗ Options import failed: {e}")
        return False
    
    try:
        from networks.RTMonoDepth.RTMonoDepth import DepthDecoder, DepthEncoder
        print("✓ RTMonoDepth baseline models imported successfully")
    except ImportError as e:
        print(f"✗ RTMonoDepth baseline import failed: {e}")
        return False
    
    try:
        from networks.RTMonoDepth.RTMonoFlow import RTMonoFlow, create_rtmonoflow
        print("✓ RTMonoFlow models imported successfully")
        return True
    except ImportError as e:
        print(f"✗ RTMonoFlow import failed: {e}")
        print("This is expected if RTMonoFlow is not properly installed")
        return False

def test_cuda():
    """Test CUDA availability"""
    print("\nTesting CUDA...")
    
    if torch.cuda.is_available():
        print(f"✓ CUDA is available")
        print(f"  - CUDA version: {torch.version.cuda}")
        print(f"  - GPU count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  - GPU {i}: {torch.cuda.get_device_name(i)}")
        return True
    else:
        print("✗ CUDA is not available")
        return False

def test_model_creation():
    """Test RTMonoFlow model creation"""
    print("\nTesting RTMonoFlow model creation...")
    
    try:
        from networks.RTMonoDepth.RTMonoFlow import create_rtmonoflow
        
        # Test adaptive HEEP model
        print("Creating RTMonoFlow with Adaptive HEEP...")
        model_adaptive = create_rtmonoflow(
            use_adaptive_heep=True,
            use_flow_blocks=True
        )
        print(f"✓ Adaptive HEEP model created successfully")
        print(f"  - Parameters: {sum(p.numel() for p in model_adaptive.parameters()):,}")
        
        # Test standard HEEP model
        print("Creating RTMonoFlow with Standard HEEP...")
        model_standard = create_rtmonoflow(
            use_adaptive_heep=False,
            use_flow_blocks=True
        )
        print(f"✓ Standard HEEP model created successfully")
        print(f"  - Parameters: {sum(p.numel() for p in model_standard.parameters()):,}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model creation failed: {e}")
        return False

def test_baseline_model():
    """Test baseline RTMonoDepth model creation"""
    print("\nTesting baseline RTMonoDepth model creation...")
    
    try:
        from networks.RTMonoDepth.RTMonoDepth import DepthDecoder, DepthEncoder
        
        encoder = DepthEncoder()
        decoder = DepthDecoder(encoder.num_ch_enc)
        
        print(f"✓ Baseline model created successfully")
        print(f"  - Encoder parameters: {sum(p.numel() for p in encoder.parameters()):,}")
        print(f"  - Decoder parameters: {sum(p.numel() for p in decoder.parameters()):,}")
        
        return True
        
    except Exception as e:
        print(f"✗ Baseline model creation failed: {e}")
        return False

def test_forward_pass():
    """Test forward pass with dummy data"""
    print("\nTesting forward pass...")
    
    try:
        from networks.RTMonoDepth.RTMonoFlow import create_rtmonoflow
        
        # Create model
        model = create_rtmonoflow(use_adaptive_heep=True, use_flow_blocks=True)
        model.eval()
        
        # Create dummy input
        dummy_input = torch.randn(1, 3, 192, 640)
        
        # Forward pass
        with torch.no_grad():
            outputs = model(dummy_input)
        
        print(f"✓ Forward pass successful")
        print(f"  - Input shape: {dummy_input.shape}")
        print(f"  - Output keys: {list(outputs.keys())}")
        if ("disp", 0) in outputs:
            print(f"  - Main output shape: {outputs[('disp', 0)].shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ Forward pass failed: {e}")
        return False

def test_evaluation_script():
    """Test if evaluation script can be imported"""
    print("\nTesting evaluation script...")
    
    try:
        # Test if evaluate_flow.py can be imported
        import evaluate_flow
        print("✓ evaluate_flow.py can be imported")
        
        # Test if main functions exist
        if hasattr(evaluate_flow, 'load_rtmonoflow_model'):
            print("✓ load_rtmonoflow_model function found")
        else:
            print("✗ load_rtmonoflow_model function not found")
            
        if hasattr(evaluate_flow, 'evaluate_model'):
            print("✓ evaluate_model function found")
        else:
            print("✗ evaluate_model function not found")
            
        return True
        
    except Exception as e:
        print(f"✗ Evaluation script import failed: {e}")
        return False

def main():
    """Run all tests"""
    print("RTMonoFlow Evaluation Environment Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("CUDA Test", test_cuda),
        ("Baseline Model Test", test_baseline_model),
        ("RTMonoFlow Model Test", test_model_creation),
        ("Forward Pass Test", test_forward_pass),
        ("Evaluation Script Test", test_evaluation_script),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n✓ All tests passed! RTMonoFlow evaluation environment is ready.")
        return True
    else:
        print(f"\n✗ {len(results) - passed} test(s) failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
