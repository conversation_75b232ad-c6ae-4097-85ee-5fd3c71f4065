#!/bin/bash

# Test script for RTMonoFlow evaluation environment
# This script activates the conda environment and runs tests

echo "RTMonoFlow Environment Test"
echo "=========================="

# Activate conda environment
echo "Activating conda environment: rtmonodepth"

# Try different conda initialization paths
if [ -f ~/miniconda3/etc/profile.d/conda.sh ]; then
    source ~/miniconda3/etc/profile.d/conda.sh
elif [ -f ~/anaconda3/etc/profile.d/conda.sh ]; then
    source ~/anaconda3/etc/profile.d/conda.sh
elif [ -f /opt/conda/etc/profile.d/conda.sh ]; then
    source /opt/conda/etc/profile.d/conda.sh
else
    # Try to use conda directly if it's in PATH
    echo "Conda initialization script not found, trying direct activation..."
fi

conda activate rtmonodepth

# Check if environment is activated
if [[ "$CONDA_DEFAULT_ENV" != "rtmonodepth" ]]; then
    echo "Error: Failed to activate rtmonodepth environment"
    echo "Please make sure the environment exists and try again"
    echo ""
    echo "To create the environment, you can use:"
    echo "conda create -n rtmonodepth python=3.8"
    echo "conda activate rtmonodepth"
    echo "pip install torch torchvision opencv-python numpy tqdm pillow scikit-image"
    exit 1
fi

echo "✓ Environment activated: $CONDA_DEFAULT_ENV"
echo ""

# Show Python and package versions
echo "Environment Information:"
echo "----------------------"
echo "Python version: $(python --version)"
echo "PyTorch version: $(python -c 'import torch; print(torch.__version__)' 2>/dev/null || echo 'Not installed')"
echo "OpenCV version: $(python -c 'import cv2; print(cv2.__version__)' 2>/dev/null || echo 'Not installed')"
echo "NumPy version: $(python -c 'import numpy; print(numpy.__version__)' 2>/dev/null || echo 'Not installed')"
echo ""

# Run the Python test script
echo "Running Python tests..."
echo "======================"
python test_evaluate_flow.py

# Check the exit code
if [ $? -eq 0 ]; then
    echo ""
    echo "✓ All tests passed! You can now run RTMonoFlow evaluation."
    echo ""
    echo "Usage examples:"
    echo "1. Test the evaluation script:"
    echo "   python evaluate_flow.py --help"
    echo ""
    echo "2. Run full evaluation (if you have trained models):"
    echo "   ./evaluate_rtmonoflow.sh"
    echo ""
    echo "3. Run evaluation with custom paths:"
    echo "   ./evaluate_rtmonoflow.sh /path/to/model /path/to/data"
else
    echo ""
    echo "✗ Some tests failed. Please check the errors above."
    echo ""
    echo "Common issues and solutions:"
    echo "1. Missing packages: Install with 'pip install package_name'"
    echo "2. RTMonoFlow import errors: Check if the networks directory is properly set up"
    echo "3. CUDA issues: Make sure PyTorch is installed with CUDA support"
fi
