#!/usr/bin/env python3
"""
实时深度估计流媒体服务器
通过Web浏览器实时查看深度估计结果
"""

import cv2
import numpy as np
import time
import threading
from flask import Flask, Response, render_template_string
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from downsetup.jetson_deployment import JetsonDepthEstimator

app = Flask(__name__)

class RealTimeDepthStreamer:
    def __init__(self):
        self.latest_frame = None
        self.latest_depth = None
        self.latest_combined = None
        self.fps = 0
        self.running = False
        self.frame_count = 0
        
    def get_csi_camera_pipeline(self, camera_id=0, width=1280, height=720, fps=30):
        """构建CSI摄像头的GStreamer管道"""
        return (
            f"nvarguscamerasrc sensor-id={camera_id} ! "
            f"video/x-raw(memory:NVMM), width={width}, height={height}, "
            f"format=NV12, framerate={fps}/1 ! "
            f"nvvidconv flip-method=0 ! "
            f"video/x-raw, width={width}, height={height}, format=BGRx ! "
            f"videoconvert ! "
            f"video/x-raw, format=BGR ! appsink"
        )
    
    def process_camera_stream(self, estimator):
        """处理摄像头流"""
        self.running = True
        
        # 打开CSI摄像头
        gst_pipeline = self.get_csi_camera_pipeline(0, 1280, 720, 30)
        cap = cv2.VideoCapture(gst_pipeline, cv2.CAP_GSTREAMER)
        
        if not cap.isOpened():
            print("❌ 无法打开CSI摄像头")
            return
        
        print("✅ 摄像头已打开，开始实时处理...")
        
        fps_history = []
        
        while self.running:
            ret, frame = cap.read()
            if not ret:
                continue
            
            # 深度估计
            depth_map, inference_time = estimator.predict_depth(frame)
            
            # 计算FPS
            fps = 1.0 / inference_time
            fps_history.append(fps)
            if len(fps_history) > 30:
                fps_history.pop(0)
            self.fps = np.mean(fps_history)
            
            # 深度图可视化
            depth_normalized = (depth_map - depth_map.min()) / (depth_map.max() - depth_map.min())
            depth_colored = cv2.applyColorMap(
                (depth_normalized * 255).astype(np.uint8),
                cv2.COLORMAP_JET
            )
            
            # 调整深度图大小
            original_h, original_w = frame.shape[:2]
            depth_colored = cv2.resize(depth_colored, (original_w, original_h))
            
            # 添加信息文本到原图
            info_text = [
                f"FPS: {self.fps:.1f}",
                f"推理时间: {inference_time*1000:.1f}ms",
                f"帧数: {self.frame_count}",
                f"分辨率: {original_w}x{original_h}"
            ]
            
            y_offset = 30
            for text in info_text:
                cv2.putText(frame, text, (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                y_offset += 25
            
            # 在深度图上添加标题
            cv2.putText(depth_colored, "Depth Map", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            # 创建并排显示的组合图像
            # 调整图像大小以适合网页显示
            display_width = 640
            display_height = int(original_h * display_width / original_w)
            
            frame_resized = cv2.resize(frame, (display_width, display_height))
            depth_resized = cv2.resize(depth_colored, (display_width, display_height))
            
            # 水平拼接
            combined = np.hstack([frame_resized, depth_resized])
            
            # 更新最新帧
            self.latest_frame = frame_resized.copy()
            self.latest_depth = depth_resized.copy()
            self.latest_combined = combined.copy()
            
            self.frame_count += 1
            
            # 控制处理频率
            time.sleep(0.01)
        
        cap.release()
        print("摄像头处理线程结束")
    
    def generate_frame_stream(self, stream_type='combined'):
        """生成视频流"""
        while True:
            if stream_type == 'combined' and self.latest_combined is not None:
                frame = self.latest_combined
            elif stream_type == 'original' and self.latest_frame is not None:
                frame = self.latest_frame
            elif stream_type == 'depth' and self.latest_depth is not None:
                frame = self.latest_depth
            else:
                # 创建占位图像
                frame = np.zeros((480, 640, 3), dtype=np.uint8)
                cv2.putText(frame, "Loading...", (250, 240),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            # 编码为JPEG
            ret, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
            if ret:
                frame_bytes = buffer.tobytes()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
            
            time.sleep(0.033)  # 约30 FPS

# 全局变量
streamer = RealTimeDepthStreamer()
depth_estimator = None

@app.route('/')
def index():
    """主页面"""
    html_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>RT-MonoDepth 实时深度估计</title>
        <meta charset="UTF-8">
        <style>
            body { 
                font-family: Arial, sans-serif; 
                margin: 0; 
                padding: 20px; 
                background-color: #f0f0f0; 
            }
            .container { 
                max-width: 1400px; 
                margin: 0 auto; 
            }
            .header { 
                text-align: center; 
                margin-bottom: 20px; 
                background: white; 
                padding: 20px; 
                border-radius: 8px; 
                box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
            }
            .video-container { 
                display: flex; 
                justify-content: center; 
                margin-bottom: 20px; 
            }
            .video-box { 
                background: white; 
                padding: 15px; 
                border-radius: 8px; 
                box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
                text-align: center;
            }
            .video-box h3 { 
                margin-top: 0; 
                color: #333; 
            }
            .stream-img { 
                max-width: 100%; 
                height: auto; 
                border: 2px solid #ddd; 
                border-radius: 4px; 
            }
            .info { 
                background: white; 
                padding: 15px; 
                margin: 10px 0; 
                border-radius: 8px; 
                box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
            }
            .status { 
                color: #28a745; 
                font-weight: bold; 
            }
            .controls {
                text-align: center;
                margin: 20px 0;
            }
            .btn {
                background: #007bff;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 4px;
                margin: 0 10px;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
            }
            .btn:hover {
                background: #0056b3;
            }
        </style>
        <script>
            function updateStatus() {
                fetch('/status')
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('fps').textContent = data.fps.toFixed(1);
                        document.getElementById('frames').textContent = data.frames;
                    })
                    .catch(error => console.log('Status update failed'));
            }
            setInterval(updateStatus, 1000); // 每秒更新状态
        </script>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 RT-MonoDepth 实时深度估计</h1>
                <p class="status">✅ 系统运行中</p>
                <p>FPS: <span id="fps">--</span> | 处理帧数: <span id="frames">--</span></p>
            </div>
            
            <div class="video-container">
                <div class="video-box">
                    <h3>📷 实时深度估计 (原图 + 深度图)</h3>
                    <img class="stream-img" src="/video_feed/combined" alt="实时深度估计">
                </div>
            </div>
            
            <div class="controls">
                <a href="/video_feed/original" class="btn" target="_blank">📷 查看原图</a>
                <a href="/video_feed/depth" class="btn" target="_blank">🎯 查看深度图</a>
                <a href="/video_feed/combined" class="btn" target="_blank">🔄 查看组合图</a>
            </div>
            
            <div class="info">
                <h3>📊 系统信息</h3>
                <p><strong>模型:</strong> RTMonoDepth</p>
                <p><strong>摄像头:</strong> CSI Camera (1280x720)</p>
                <p><strong>处理分辨率:</strong> 640x192</p>
                <p><strong>显示分辨率:</strong> 640x360 (每个视图)</p>
                <p><strong>访问地址:</strong> http://开发板IP:5000</p>
            </div>
            
            <div class="info">
                <h3>🎮 使用说明</h3>
                <p>• 主画面显示原图和深度图的并排对比</p>
                <p>• 深度图中红色表示近距离，蓝色表示远距离</p>
                <p>• 可以点击按钮单独查看原图或深度图</p>
                <p>• 支持多设备同时访问</p>
                <p>• 实时更新，无需刷新页面</p>
            </div>
        </div>
    </body>
    </html>
    """
    return render_template_string(html_template)

@app.route('/video_feed/<stream_type>')
def video_feed(stream_type):
    """视频流端点"""
    return Response(streamer.generate_frame_stream(stream_type),
                   mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/status')
def status():
    """状态API"""
    return {
        'fps': streamer.fps,
        'frames': streamer.frame_count,
        'running': streamer.running
    }

def main():
    global depth_estimator
    
    print("🚀 启动实时深度估计流媒体服务器...")
    
    # 模拟命令行参数
    class Args:
        model_type = 'small'
        weights_folder = 'weights/RTMonoDepth/s/m_640_192/'
        camera_params = 'my_camera_params.txt'
        input_height = 192
        input_width = 640
        use_tensorrt = True
        force_convert = False
        benchmark_cycles = 200
        camera_test = True
        camera_id = 0
        use_csi = True
    
    args = Args()
    
    try:
        # 创建深度估计器
        print("正在初始化深度估计器...")
        depth_estimator = JetsonDepthEstimator(args)
        print("✅ 深度估计器初始化完成")
        
        # 启动摄像头处理线程
        camera_thread = threading.Thread(target=streamer.process_camera_stream, args=(depth_estimator,))
        camera_thread.daemon = True
        camera_thread.start()
        print("✅ 摄像头处理线程已启动")
        
        print("🌐 Web服务器启动中...")
        print("📱 请在浏览器中访问:")
        print("   http://************:5000")
        print("   或 http://开发板IP:5000")
        print("\n按 Ctrl+C 停止服务器")
        
        # 启动Web服务器
        app.run(host='0.0.0.0', port=5000, debug=False, threaded=True)
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
    finally:
        streamer.running = False

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        streamer.running = False
        print("\n🛑 服务器已停止")
