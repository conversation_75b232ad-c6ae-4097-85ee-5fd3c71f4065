Weight,abs_rel,sq_rel,rmse,rmse_log,a1,a2,a3
weights_0,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_0
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_0
-> Loaded RTMonoFlow weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_0/rtmonoflow.pth
-> Evaluating RTMonoFlow (adaptive)
-> RTMonoFlow (adaptive) average inference time: 135.20 ms (7.4 FPS)
-> RTMonoFlow evaluation completed

====================================================================================================
RTMONOFLOW EVALUATION RESULTS
====================================================================================================
Model                     abs_rel  sq_rel   rmse     rmse_log a1       a2       a3       FPS     
----------------------------------------------------------------------------------------------------
RTMonoFlow (adaptive)     0.129    1.006    5.150    0.217    0.834    0.942    0.974    7.4     

-> RTMonoFlow evaluation completed!

weights_1,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_1
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_1
-> Loaded RTMonoFlow weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_1/rtmonoflow.pth
-> Evaluating RTMonoFlow (adaptive)
-> RTMonoFlow (adaptive) average inference time: 127.58 ms (7.8 FPS)
-> RTMonoFlow evaluation completed

====================================================================================================
RTMONOFLOW EVALUATION RESULTS
====================================================================================================
Model                     abs_rel  sq_rel   rmse     rmse_log a1       a2       a3       FPS     
----------------------------------------------------------------------------------------------------
RTMonoFlow (adaptive)     0.130    1.014    5.192    0.220    0.831    0.942    0.974    7.8     

-> RTMonoFlow evaluation completed!

weights_2,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_2
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_2
-> Loaded RTMonoFlow weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_2/rtmonoflow.pth
-> Evaluating RTMonoFlow (adaptive)
-> RTMonoFlow (adaptive) average inference time: 126.18 ms (7.9 FPS)
-> RTMonoFlow evaluation completed

====================================================================================================
RTMONOFLOW EVALUATION RESULTS
====================================================================================================
Model                     abs_rel  sq_rel   rmse     rmse_log a1       a2       a3       FPS     
----------------------------------------------------------------------------------------------------
RTMonoFlow (adaptive)     0.127    0.998    5.159    0.217    0.835    0.943    0.974    7.9     

-> RTMonoFlow evaluation completed!

weights_3,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_3
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_3
-> Loaded RTMonoFlow weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_3/rtmonoflow.pth
-> Evaluating RTMonoFlow (adaptive)
-> RTMonoFlow (adaptive) average inference time: 124.74 ms (8.0 FPS)
-> RTMonoFlow evaluation completed

====================================================================================================
RTMONOFLOW EVALUATION RESULTS
====================================================================================================
Model                     abs_rel  sq_rel   rmse     rmse_log a1       a2       a3       FPS     
----------------------------------------------------------------------------------------------------
RTMonoFlow (adaptive)     0.128    1.027    5.190    0.219    0.833    0.942    0.974    8.0     

-> RTMonoFlow evaluation completed!

weights_4,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_4
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_4
-> Loaded RTMonoFlow weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_4/rtmonoflow.pth
-> Evaluating RTMonoFlow (adaptive)
-> RTMonoFlow (adaptive) average inference time: 124.05 ms (8.1 FPS)
-> RTMonoFlow evaluation completed

====================================================================================================
RTMONOFLOW EVALUATION RESULTS
====================================================================================================
Model                     abs_rel  sq_rel   rmse     rmse_log a1       a2       a3       FPS     
----------------------------------------------------------------------------------------------------
RTMonoFlow (adaptive)     0.131    1.003    5.172    0.221    0.830    0.942    0.974    8.1     

-> RTMonoFlow evaluation completed!

weights_5,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_5
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_5
-> Loaded RTMonoFlow weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_5/rtmonoflow.pth
-> Evaluating RTMonoFlow (adaptive)
-> RTMonoFlow (adaptive) average inference time: 123.61 ms (8.1 FPS)
-> RTMonoFlow evaluation completed

====================================================================================================
RTMONOFLOW EVALUATION RESULTS
====================================================================================================
Model                     abs_rel  sq_rel   rmse     rmse_log a1       a2       a3       FPS     
----------------------------------------------------------------------------------------------------
RTMonoFlow (adaptive)     0.128    1.029    5.186    0.218    0.837    0.943    0.974    8.1     

-> RTMonoFlow evaluation completed!

weights_6,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_6
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_6
-> Loaded RTMonoFlow weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_6/rtmonoflow.pth
-> Evaluating RTMonoFlow (adaptive)
-> RTMonoFlow (adaptive) average inference time: 125.58 ms (8.0 FPS)
-> RTMonoFlow evaluation completed

====================================================================================================
RTMONOFLOW EVALUATION RESULTS
====================================================================================================
Model                     abs_rel  sq_rel   rmse     rmse_log a1       a2       a3       FPS     
----------------------------------------------------------------------------------------------------
RTMonoFlow (adaptive)     0.130    1.019    5.180    0.218    0.835    0.943    0.974    8.0     

-> RTMonoFlow evaluation completed!

weights_7,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_7
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_7
-> Loaded RTMonoFlow weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_7/rtmonoflow.pth
-> Evaluating RTMonoFlow (adaptive)
-> RTMonoFlow (adaptive) average inference time: 124.66 ms (8.0 FPS)
-> RTMonoFlow evaluation completed

====================================================================================================
RTMONOFLOW EVALUATION RESULTS
====================================================================================================
Model                     abs_rel  sq_rel   rmse     rmse_log a1       a2       a3       FPS     
----------------------------------------------------------------------------------------------------
RTMonoFlow (adaptive)     0.131    1.015    5.198    0.220    0.832    0.942    0.974    8.0     

-> RTMonoFlow evaluation completed!

weights_8,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_8
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_8
-> Loaded RTMonoFlow weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_8/rtmonoflow.pth
-> Evaluating RTMonoFlow (adaptive)
-> RTMonoFlow (adaptive) average inference time: 134.93 ms (7.4 FPS)
-> RTMonoFlow evaluation completed

====================================================================================================
RTMONOFLOW EVALUATION RESULTS
====================================================================================================
Model                     abs_rel  sq_rel   rmse     rmse_log a1       a2       a3       FPS     
----------------------------------------------------------------------------------------------------
RTMonoFlow (adaptive)     0.130    1.026    5.162    0.218    0.835    0.943    0.974    7.4     

-> RTMonoFlow evaluation completed!

weights_9,,,,,,,
Using device: cuda
Stereo evaluation - disabling median scaling, scaling by 5.4
-> Loading weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_9
-> Computing predictions with size 640x192
-> Evaluating 697 images
-> Loading RTMonoFlow (adaptive) from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_9
-> Loaded RTMonoFlow weights from /mnt/acer/RT-MonoDepth-main/log/rtmonoflow/rtmonoflow_adaptive_heep_finetune/models/weights_9/rtmonoflow.pth
-> Evaluating RTMonoFlow (adaptive)
-> RTMonoFlow (adaptive) average inference time: 130.81 ms (7.6 FPS)
-> RTMonoFlow evaluation completed

====================================================================================================
RTMONOFLOW EVALUATION RESULTS
====================================================================================================
Model                     abs_rel  sq_rel   rmse     rmse_log a1       a2       a3       FPS     
----------------------------------------------------------------------------------------------------
RTMonoFlow (adaptive)     0.130    1.021    5.174    0.218    0.834    0.943    0.974    7.6     

-> RTMonoFlow evaluation completed!

